server:
  # 服务器的HTTP端口，默认为9999
  port: 9999
  ssl:
    enabled: false
    key-store: classpath:sxwjsm.com.pfx
    key-store-type: PKCS12
    key-store-password: f7mo0uu4920i

# Spring Boot DevTools 热部署配置
spring:
  devtools:
    restart:
      enabled: true  # 启用热重启
      additional-paths: src/main/java  # 监控的路径
      exclude: static/**,public/**  # 排除静态资源
    livereload:
      enabled: true  # 启用LiveReload
      port: 35729    # LiveReload端口
  # 模板缓存禁用（开发环境）
  thymeleaf:
    cache: false
  freemarker:
    cache: false

  # 缓存配置
  cache:
    type: simple  # 使用简单缓存（开发环境）
    cache-names:
      - goodsPageInfo
      - goodsDetail
      - goodsCategory
      - entityClass
      - homeIndex

  # 邮件配置
  mail:
    host: smtp.qq.com  # 邮件服务器地址，这里以QQ邮箱为例
    port: 587  # 邮件服务器端口
    username: <EMAIL>  # 发送方邮箱地址
    password: emokugphskhsfjci  # QQ邮箱授权码（不是登录密码）
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            enable: false
        debug: true  # 开启调试模式，便于排查问题

custom:
  http:
    port: 80

# 微信小程序配置
wx:
  miniapp:
    configs:
      - appid: wxca19c2771bc2ea41  # 替换为您的小程序appid
        secret: 6b6a137357e200e7a8fb48fac540b1e1 # 替换为您的小程序secret
        token: # 微信小程序消息服务器配置的token
        aesKey: # 微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON # 消息格式，可选值：XML，JSON
    # 如果需要使用订阅消息，请申请并配置模板ID
    subscribe-template-id: # 订阅消息模板ID，留空表示不使用订阅消息
    # 订单成功通知模板ID，需要在微信公众平台申请并替换
    # 示例格式 "1h5fDW-ExMwXXXXXXX-XXXXXXXX-XXXXXXs"
    order-success-template-id: lkNSGD-0WrxpJWzl0u8MrdkwBfy8-BCVrtMDQJl7zJk  # 请替换为您申请的实际模板ID

  # 微信支付配置
  pay:
    appId: wxca19c2771bc2ea41  # 小程序appid，与上面保持一致
    mchId: 1717878370  # 替换为您的微信支付商户号
    apiV3Key: fqhh3524fwr6652yhewafag254341345
    privateCertPath: classpath:/cert/apiclient_cert.pem
    privateKeyPath: classpath:/cert/apiclient_key.pem
    notifyUrl: https://www.sxwjsm.com/weshop-wjhx/wechat/pay/notify


  # 管理员配置
  admin:
    # 运维人员的微信OpenID列表，多个ID用逗号分隔
    operation-staff-openids:
      - oL5Oo1PXf2nF_NbLYT5-7PGJQwY_  # 替换为实际运维人员的OpenID
      - oL5Oo1PXf2nF_SAMPLE_ID_XYZ  # 替换为实际运维人员的OpenID

# 应用自定义配置
app:
  # 邮件配置
  email:
    default-from: <EMAIL>  # 默认发送方邮箱
    default-from-name: 伍俊惠选  # 默认发送方名称
    default-recipients:  # 默认收件人列表（系统通知）
      - <EMAIL>
    template:
      path: classpath:/templates/email/
      default-template: default.html
    attachment:
      max-size: 25  # 附件最大大小（MB）
      allowed-types:
        - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
        - application/vnd.ms-excel
        - application/pdf
        - text/csv
        - application/zip

# 文件上传配置
file:
  upload:
    path: ./uploads  # 文件上传路径
    url-prefix: https://www.sxwjsm.com/weshop-wjhx/uploads  # 文件访问URL前缀

# JWT Token配置
weshop:
  jwt:
    # JWT密钥
    secret: asdqweasasdqweasasdqweasasdqweasasdqweasasdqweasasdqweasasdqweas
    # Token有效期（毫秒），默认7天
    ttl: 604800000  # 7 * 24 * 60 * 60 * 1000
    # Token刷新阈值（毫秒），当token剩余有效期小于此时间时，可以刷新，默认2天
    refresh-threshold: 172800000  # 2 * 24 * 60 * 60 * 1000
    # 是否启用自动刷新
    auto-refresh-enabled: true
    # 是否启用过期token刷新
    expired-refresh-enabled: true
    # 过期token可刷新的时间窗口（毫秒），默认1小时
    expired-refresh-window: 3600000  # 60 * 60 * 1000
    # Token请求头名称
    header-name: X-Weshop-Token
    # 新Token响应头名称
    new-token-header-name: X-New-Token
    # 是否在日志中记录token操作
    log-enabled: true
    # 是否启用token状态检查接口
    status-check-enabled: true
    # 是否启用手动刷新接口
    manual-refresh-enabled: true

