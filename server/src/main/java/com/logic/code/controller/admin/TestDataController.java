package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.entity.goods.Goods;
import com.logic.code.entity.goods.GoodsSpecification;
import com.logic.code.entity.goods.Product;
import com.logic.code.service.GoodsService;
import com.logic.code.service.GoodsSpecificationService;
import com.logic.code.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 测试数据控制器
 * 用于插入测试数据
 */
@RestController
@RequestMapping("/adminapi/test")
public class TestDataController {

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private GoodsSpecificationService goodsSpecificationService;

    @Autowired
    private ProductService productService;

    /**
     * 插入测试数据
     */
    @PostMapping("/insertTestData")
    public Result<String> insertTestData() {
        try {
            // 插入测试商品
            List<Goods> goodsList = new ArrayList<>();
            
            Goods goods1 = Goods.builder()
                    .id(1)
                    .categoryId(1)
                    .goodsSn("TEST001")
                    .name("测试商品1")
                    .brandId(1)
                    .goodsNumber(100)
                    .keywords("测试")
                    .goodsBrief("这是一个测试商品")
                    .isOnSale(true)
                    .sortOrder((short) 1)
                    .isDelete(false)
                    .attributeCategory(1)
                    .counterPrice(new BigDecimal("120.00"))
                    .extraPrice(new BigDecimal("0.00"))
                    .isNewly(false)
                    .goodsUnit("个")
                    .primaryPicUrl("test1.jpg")
                    .listPicUrl("test1_list.jpg")
                    .retailPrice(new BigDecimal("100.00"))
                    .sellVolume(0)
                    .primaryProductId(1)
                    .unitPrice(new BigDecimal("100.00"))
                    .promotionDesc("")
                    .promotionTag("")
                    .appExclusivePrice(new BigDecimal("0.00"))
                    .isAppExclusive(false)
                    .isLimited(false)
                    .isHot(false)
                    .goodsDesc("测试商品详情")
                    .displaySystems("[1,2,3]")
                    .build();

            Goods goods2 = Goods.builder()
                    .id(2)
                    .categoryId(1)
                    .goodsSn("TEST002")
                    .name("测试商品2")
                    .brandId(1)
                    .goodsNumber(50)
                    .keywords("测试")
                    .goodsBrief("这是另一个测试商品")
                    .isOnSale(true)
                    .sortOrder((short) 2)
                    .isDelete(false)
                    .attributeCategory(1)
                    .counterPrice(new BigDecimal("200.00"))
                    .extraPrice(new BigDecimal("0.00"))
                    .isNewly(false)
                    .goodsUnit("个")
                    .primaryPicUrl("test2.jpg")
                    .listPicUrl("test2_list.jpg")
                    .retailPrice(new BigDecimal("180.00"))
                    .sellVolume(0)
                    .primaryProductId(2)
                    .unitPrice(new BigDecimal("180.00"))
                    .promotionDesc("")
                    .promotionTag("")
                    .appExclusivePrice(new BigDecimal("0.00"))
                    .isAppExclusive(false)
                    .isLimited(false)
                    .isHot(false)
                    .goodsDesc("测试商品2详情")
                    .displaySystems("[1,2]")
                    .build();

            goodsList.add(goods1);
            goodsList.add(goods2);

            // 保存商品
            for (Goods goods : goodsList) {
                goodsService.insert(goods);
            }

            // 插入商品规格数据
            List<GoodsSpecification> specList = new ArrayList<>();
            
            // 商品1的规格：颜色和尺寸
            specList.add(GoodsSpecification.builder().goodsId(1).specificationId(1).value("红色").picUrl("red.jpg").build());
            specList.add(GoodsSpecification.builder().goodsId(1).specificationId(1).value("蓝色").picUrl("blue.jpg").build());
            specList.add(GoodsSpecification.builder().goodsId(1).specificationId(1).value("绿色").picUrl("green.jpg").build());
            specList.add(GoodsSpecification.builder().goodsId(1).specificationId(2).value("S").picUrl("").build());
            specList.add(GoodsSpecification.builder().goodsId(1).specificationId(2).value("M").picUrl("").build());
            specList.add(GoodsSpecification.builder().goodsId(1).specificationId(2).value("L").picUrl("").build());
            
            // 商品2的规格：颜色和型号
            specList.add(GoodsSpecification.builder().goodsId(2).specificationId(1).value("黑色").picUrl("black.jpg").build());
            specList.add(GoodsSpecification.builder().goodsId(2).specificationId(1).value("白色").picUrl("white.jpg").build());
            specList.add(GoodsSpecification.builder().goodsId(2).specificationId(4).value("A型").picUrl("").build());
            specList.add(GoodsSpecification.builder().goodsId(2).specificationId(4).value("B型").picUrl("").build());

            // 保存规格数据
            for (GoodsSpecification spec : specList) {
                goodsSpecificationService.insert(spec);
            }

            // 插入产品SKU数据
            List<Product> productList = new ArrayList<>();
            
            // 商品1的SKU
            productList.add(Product.builder().id(1).goodsId(1).goodsSpecificationIds("1,4").goodsSn("TEST001-1").goodsNumber(20).retailPrice(new BigDecimal("100.00")).costPrice(new BigDecimal("50.00")).otPrice(new BigDecimal("120.00")).barCode("BC001").barCodeNumber("123456789001").goodsWeight(new BigDecimal("0.50")).goodsVolume(new BigDecimal("0.01")).picUrl("red_s.jpg").brokerage(new BigDecimal("5.00")).brokerageTwo(new BigDecimal("2.00")).quota(0).quotaShow(false).build());
            productList.add(Product.builder().id(2).goodsId(1).goodsSpecificationIds("1,5").goodsSn("TEST001-2").goodsNumber(25).retailPrice(new BigDecimal("100.00")).costPrice(new BigDecimal("50.00")).otPrice(new BigDecimal("120.00")).barCode("BC002").barCodeNumber("123456789002").goodsWeight(new BigDecimal("0.50")).goodsVolume(new BigDecimal("0.01")).picUrl("red_m.jpg").brokerage(new BigDecimal("5.00")).brokerageTwo(new BigDecimal("2.00")).quota(0).quotaShow(false).build());
            productList.add(Product.builder().id(3).goodsId(1).goodsSpecificationIds("1,6").goodsSn("TEST001-3").goodsNumber(30).retailPrice(new BigDecimal("100.00")).costPrice(new BigDecimal("50.00")).otPrice(new BigDecimal("120.00")).barCode("BC003").barCodeNumber("123456789003").goodsWeight(new BigDecimal("0.50")).goodsVolume(new BigDecimal("0.01")).picUrl("red_l.jpg").brokerage(new BigDecimal("5.00")).brokerageTwo(new BigDecimal("2.00")).quota(0).quotaShow(false).build());

            // 商品2的SKU
            productList.add(Product.builder().id(7).goodsId(2).goodsSpecificationIds("7,9").goodsSn("TEST002-1").goodsNumber(10).retailPrice(new BigDecimal("180.00")).costPrice(new BigDecimal("90.00")).otPrice(new BigDecimal("200.00")).barCode("BC007").barCodeNumber("123456789007").goodsWeight(new BigDecimal("1.00")).goodsVolume(new BigDecimal("0.02")).picUrl("black_a.jpg").brokerage(new BigDecimal("10.00")).brokerageTwo(new BigDecimal("5.00")).quota(0).quotaShow(false).build());
            productList.add(Product.builder().id(8).goodsId(2).goodsSpecificationIds("7,10").goodsSn("TEST002-2").goodsNumber(15).retailPrice(new BigDecimal("180.00")).costPrice(new BigDecimal("90.00")).otPrice(new BigDecimal("200.00")).barCode("BC008").barCodeNumber("123456789008").goodsWeight(new BigDecimal("1.00")).goodsVolume(new BigDecimal("0.02")).picUrl("black_b.jpg").brokerage(new BigDecimal("10.00")).brokerageTwo(new BigDecimal("5.00")).quota(0).quotaShow(false).build());

            // 保存产品数据
            for (Product product : productList) {
                productService.insert(product);
            }

            return Result.success("测试数据插入成功");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("测试数据插入失败: " + e.getMessage());
        }
    }
}
