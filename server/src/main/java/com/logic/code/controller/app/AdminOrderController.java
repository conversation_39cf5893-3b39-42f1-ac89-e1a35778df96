package com.logic.code.controller.app;

import com.logic.code.common.enmus.OrderStatusEnum;
import com.logic.code.common.enmus.WeshopWechatResultStatus;
import com.logic.code.common.exception.WeshopWechatException;
import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderGoods;
import com.logic.code.model.query.AdminOrderQuery;
import com.logic.code.model.vo.AdminOrderListVO;
import com.logic.code.model.vo.ExpressCompanyVO;
import com.logic.code.model.vo.OrderListVO;
import com.logic.code.service.ExpressCompanyService;
import com.logic.code.service.OrderGoodsService;
import com.logic.code.service.OrderService;
import com.logic.code.service.UserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 管理员订单控制器
 * <AUTHOR>
 * @date 2025/1/27
 */
@RestController
@RequestMapping("/admin/order")
@Slf4j
public class









AdminOrderController {

    @Resource
    private OrderService orderService;

    @Resource
    private OrderGoodsService orderGoodsService;

    @Resource
    private UserService userService;

    @Resource
    private ExpressCompanyService expressCompanyService;

    /**
     * 管理员订单列表
     */
    @GetMapping("/list")
    public Object list(AdminOrderQuery query) {
        // 检查管理员权限
        User userInfo = JwtHelper.getUserInfo();
 /*       if (userInfo == null || userInfo.getUserLevelId() == null || !userInfo.getUserLevelId().equals(1)) {
            throw new WeshopWechatException(WeshopWechatResultStatus.UNAUTHORIZED);
        }*/

        try {
            // 获取所有订单
            List<Order> allOrders = orderService.queryAll();

            // 过滤和搜索
            List<Order> filteredOrders = allOrders.stream()
                .filter(order -> {
                    // 状态过滤
                    if (query.getOrderStatus() != null && !query.getOrderStatus().isEmpty()) {
                        return (order.getOrderStatus().getValue()+"").equals(query.getOrderStatus());
                    }
                    return true;
                })
                .filter(order -> {
                    // 关键词搜索：订单号、用户昵称、商品名称
                    if (query.getKeyword() != null && !query.getKeyword().trim().isEmpty()) {
                        String keyword = query.getKeyword().toLowerCase().trim();

                        // 搜索订单号
                        if (order.getOrderSn().toLowerCase().contains(keyword)) {
                            return true;
                        }

                        // 搜索用户昵称
                        User orderUser = userService.queryById(order.getUserId());
                        if (orderUser != null && orderUser.getNickname() != null &&
                            orderUser.getNickname().toLowerCase().contains(keyword)) {
                            return true;
                        }

                        // 搜索商品名称
                        List<OrderGoods> orderGoodsList = orderGoodsService.queryList(
                            OrderGoods.builder().orderId(order.getId()).build()
                        );
                        boolean hasMatchingGoods = orderGoodsList.stream()
                            .anyMatch(goods -> goods.getGoodsName() != null &&
                                     goods.getGoodsName().toLowerCase().contains(keyword));

                        return hasMatchingGoods;
                    }
                    return true;
                })
                .collect(Collectors.toList());

            // 排序处理
            filteredOrders = applySorting(filteredOrders, query.getSortField(), query.getSortOrder());

            // 分页处理
            int pageNum = query.getPageNum() != null ? query.getPageNum() : 1;
            int pageSize = query.getPageSize() != null ? query.getPageSize() : 10;
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, filteredOrders.size());

            List<Order> pagedOrders = filteredOrders.subList(startIndex, endIndex);

            // 构建返回数据
            List<AdminOrderListVO> orderVOList = new ArrayList<>();
            for (Order order : pagedOrders) {
                User orderUser = userService.queryById(order.getUserId());
                List<OrderGoods> orderGoodsList = orderGoodsService.queryList(
                    OrderGoods.builder().orderId(order.getId()).build()
                );

                AdminOrderListVO orderVO = new AdminOrderListVO();
                orderVO.setId(order.getId());
                orderVO.setOrderSn(order.getOrderSn());
                orderVO.setOrderStatus(order.getOrderStatus().getValue());
                orderVO.setOrderStatusText(order.getOrderStatus().getName());
                orderVO.setOrderPrice(order.getOrderPrice());
                orderVO.setCreateTime(order.getCreateTime().toString());
                orderVO.setUserName(orderUser != null ? orderUser.getNickname() : "未知用户");
                orderVO.setUserAvatar(orderUser != null ? orderUser.getAvatar() : null);
                orderVO.setUserMobile(orderUser != null ? orderUser.getMobile() : null);
                orderVO.setGoodsList(orderGoodsList);

                orderVOList.add(orderVO);
            }

            // 构建分页信息
            AdminOrderQuery.PageResult result = new AdminOrderQuery.PageResult();
            result.setList(orderVOList);
            result.setPageNum(pageNum);
            result.setPageSize(pageSize);
            result.setTotal(filteredOrders.size());
            result.setPages((int) Math.ceil((double) filteredOrders.size() / pageSize));

            return Result.success(result);

        } catch (Exception e) {
            log.error("获取管理员订单列表失败: {}", e.getMessage(), e);
            throw new WeshopWechatException(WeshopWechatResultStatus.SYSTEM_ERROR);
        }
    }


    /**
     * 管理员发货订单
     */
    @PutMapping("/delivery/{id}")
    public Result<String> delivery(@PathVariable("id") Integer orderId, @RequestBody Object deliveryData) {
        // 检查管理员权限
        User userInfo = JwtHelper.getUserInfo();
        if (userInfo == null || userInfo.getUserLevelId() == null || !userInfo.getUserLevelId().equals(1)) {
            throw new WeshopWechatException(WeshopWechatResultStatus.UNAUTHORIZED);
        }

        try {
            // 简化版发货，只更新订单状态
            Order order = orderService.queryById(orderId);
            if (order == null) {
                return Result.failure("订单不存在");
            }

            if (order.getOrderStatus().getValue() != 1) {
                return Result.failure("订单状态不允许发货");
            }

            // 更新订单状态为待收货
            order.setOrderStatus(OrderStatusEnum.WAIT_RECEIVE);
            order.setShippingStatus((short) 1);
            order.setShippingTime(new java.util.Date());
            orderService.updateById(order);

            log.info("管理员{}发货订单{}成功", userInfo.getId(), orderId);
            return Result.success("发货成功");
        } catch (Exception e) {
            log.error("管理员发货失败: {}", e.getMessage(), e);
            return Result.failure("发货失败：" + e.getMessage());
        }
    }

    /**
     * 管理员取消订单
     */
    @PostMapping("/cancel")
    public Object cancel(@RequestParam Integer orderId) {
        // 检查管理员权限
        User userInfo = JwtHelper.getUserInfo();
        if (userInfo == null || userInfo.getUserLevelId() == null || !userInfo.getUserLevelId().equals(1)) {
            throw new WeshopWechatException(WeshopWechatResultStatus.UNAUTHORIZED);
        }

        try {
            // 这里应该调用订单服务的取消方法
             orderService.cancel(orderId);
            log.info("管理员{}取消订单{}", userInfo.getId(), orderId);
            return "取消成功";
        } catch (Exception e) {
            log.error("管理员取消订单失败: {}", e.getMessage(), e);
            throw new WeshopWechatException(WeshopWechatResultStatus.SYSTEM_ERROR);
        }
    }

    /**
     * 获取快递公司列表
     */
    @GetMapping("/kuaidi_coms")
    public Result<List<ExpressCompanyVO>> getExpressCompanyList() {
        try {
            // 从数据库获取启用的快递公司列表
            List<ExpressCompanyVO> expressList = expressCompanyService.getEnabledExpressCompanies();
            
            // 如果数据库中没有数据，返回默认列表
            if (expressList.isEmpty()) {
                expressList = getDefaultExpressCompanies();
                log.warn("数据库中没有快递公司数据，使用默认列表");
            }
            
            log.info("获取快递公司列表成功，共{}家", expressList.size());
            return Result.success(expressList);
        } catch (Exception e) {
            log.error("获取快递公司列表失败: {}", e.getMessage(), e);
            // 发生异常时返回默认列表，确保功能可用
            List<ExpressCompanyVO> defaultList = getDefaultExpressCompanies();
            log.warn("使用默认快递公司列表，共{}家", defaultList.size());
            return Result.success(defaultList);
        }
    }

    /**
     * 获取默认快递公司列表（备用方案）
     * @return 快递公司列表
     */
    private List<ExpressCompanyVO> getDefaultExpressCompanies() {
        List<ExpressCompanyVO> expressList = new ArrayList<>();
        
        // 添加常用快递公司
        expressList.add(ExpressCompanyVO.builder().value("顺丰速运").code("SF").id(1).sort(1).build());
        expressList.add(ExpressCompanyVO.builder().value("中通快递").code("ZTO").id(2).sort(2).build());
        expressList.add(ExpressCompanyVO.builder().value("圆通速递").code("YTO").id(3).sort(3).build());
        expressList.add(ExpressCompanyVO.builder().value("申通快递").code("STO").id(4).sort(4).build());
        expressList.add(ExpressCompanyVO.builder().value("韵达速递").code("YD").id(5).sort(5).build());
        expressList.add(ExpressCompanyVO.builder().value("百世快递").code("HTKY").id(6).sort(6).build());
        expressList.add(ExpressCompanyVO.builder().value("德邦快递").code("DBL").id(7).sort(7).build());
        expressList.add(ExpressCompanyVO.builder().value("京东快递").code("JD").id(8).sort(8).build());
        expressList.add(ExpressCompanyVO.builder().value("邮政快递包裹").code("YZPY").id(9).sort(9).build());
        expressList.add(ExpressCompanyVO.builder().value("EMS").code("EMS").id(10).sort(10).build());
        expressList.add(ExpressCompanyVO.builder().value("天天快递").code("HHTT").id(11).sort(11).build());
        expressList.add(ExpressCompanyVO.builder().value("宅急送").code("ZJS").id(12).sort(12).build());
        expressList.add(ExpressCompanyVO.builder().value("国通快递").code("GTO").id(13).sort(13).build());
        expressList.add(ExpressCompanyVO.builder().value("全峰快递").code("QFKD").id(14).sort(14).build());
        expressList.add(ExpressCompanyVO.builder().value("优速快递").code("UC").id(15).sort(15).build());
        expressList.add(ExpressCompanyVO.builder().value("中国快递服务").code("CCES").id(16).sort(16).build());
        expressList.add(ExpressCompanyVO.builder().value("安能快递").code("ANE").id(17).sort(17).build());
        expressList.add(ExpressCompanyVO.builder().value("快捷快递").code("FAST").id(18).sort(18).build());
        expressList.add(ExpressCompanyVO.builder().value("ADP国际快递").code("ADP").id(19).sort(19).build());
        expressList.add(ExpressCompanyVO.builder().value("DHL").code("DHL").id(20).sort(20).build());
        
        return expressList;
    }

    /**
     * 应用排序逻辑
     * @param orders 订单列表
     * @param sortField 排序字段
     * @param sortOrder 排序方式
     * @return 排序后的订单列表
     */
    private List<Order> applySorting(List<Order> orders, String sortField, String sortOrder) {
        if (orders == null || orders.isEmpty()) {
            return orders;
        }

        // 默认排序字段和方式
        if (sortField == null || sortField.trim().isEmpty()) {
            sortField = "createTime";
        }
        if (sortOrder == null || sortOrder.trim().isEmpty()) {
            sortOrder = "desc";
        }

        try {
            Comparator<Order> comparator;

            // 根据排序字段选择比较器
            switch (sortField.toLowerCase()) {
                case "createtime":
                default:
                    // 按创建时间排序
                    comparator = Comparator.comparing(Order::getCreateTime);
                    break;
            }

            // 根据排序方式决定是否反转
            if ("asc".equalsIgnoreCase(sortOrder)) {
                // 正序：最早的在前面
                return orders.stream().sorted(comparator).collect(Collectors.toList());
            } else {
                // 倒序：最新的在前面（默认）
                return orders.stream().sorted(comparator.reversed()).collect(Collectors.toList());
            }

        } catch (Exception e) {
            log.warn("排序失败，使用默认排序: {}", e.getMessage());
            // 排序失败时，使用默认的创建时间倒序
            return orders.stream()
                    .sorted(Comparator.comparing(Order::getCreateTime).reversed())
                    .collect(Collectors.toList());
        }
    }
}