package com.logic.code.schedule;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.logic.code.common.utils.StringUtils;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderExpress;
import com.logic.code.service.OrderExpressService;
import com.logic.code.service.OrderExportService;
import com.logic.code.service.OrderService;
import com.logic.code.service.SalesVolumeUpdateService;
import com.logic.code.service.TokenService;
import com.logic.code.service.WxOrderShippingService;
import com.logic.code.service.WxShippingInfoService;
import com.logic.code.service.UserService;
import com.logic.code.entity.User;
import com.logic.code.entity.order.WxShippingInfo;
import com.logic.code.model.dto.WxShippingInfoDTO;
import com.logic.code.common.enmus.OrderStatusEnum;
import jakarta.annotation.Resource;
import logic.orm.utils.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/31 20:20
 * @desc
 */

@Component
@Slf4j
public class TaskSchedule {


    @Resource
    OrderExpressService orderExpressService;

    @Resource
    OrderService orderService;

    @Resource
    OrderExportService orderExportService;

    @Resource
    SalesVolumeUpdateService salesVolumeUpdateService;

    @Resource
    TokenService tokenService;

    @Resource
    WxOrderShippingService wxOrderShippingService;

    @Resource
    WxShippingInfoService wxShippingInfoService;

    @Resource
    UserService userService;

    @Scheduled(fixedRate = 4 * 60 * 60 * 1000)
    public void test() {

        List<OrderExpress> list = orderExpressService.queryAll();
        if (ListUtils.isNotBlank(list)) {
            for (OrderExpress express : list) {
                if (!"SIGN".equals(express.getLogisticsStatus())) {
                    Order order = orderService.queryById(express.getOrderId());
                    if(order== null) continue;
                    String mobile = order.getMobile();
                    String trace = getTrace(express.getLogisticCode(), mobile);
                    if (StringUtils.isBlank(trace)) continue;
                    List<Trace> traceList = new ArrayList<>();
                    JSONObject jsonObject = JSONObject.parseObject(trace);
                    if (jsonObject.getInteger("code") == 200) {
                        JSONObject data = jsonObject.getJSONObject("data");
                        String logisticsStatus = data.getString("logisticsStatus");
                        express.setLogisticsStatus(logisticsStatus);
                        JSONArray traceDetailList = data.getJSONArray("logisticsTraceDetailList");
                        for (int i = 0; i < traceDetailList.size(); i++) {
                            JSONObject routeDetail = traceDetailList.getJSONObject(i);
                            String desc = routeDetail.getString("desc");
                            String status = routeDetail.getString("logisticsStatus");
                            Long time = routeDetail.getLong("time");
                            traceList.add(new Trace(desc, DateUtil.formatDateTime(new Date(time)), status));
                        }
                        if (ListUtils.isNotBlank(traceList)) {
                            traceList = traceList.stream().sorted(Comparator.comparing(Trace::getDatetime).reversed()).collect(Collectors.toList());
                            express.setTraces(JSONObject.toJSONString(traceList));
                        }

                        orderExpressService.updateNotNull(express);
                    }
                }
            }
        }
    }


    public String getTrace(String no, String mobile) {
        String host = "https://kzexpress.market.alicloudapi.com";
        String path = "/api-mall/api/express/query";
        String appcode = "80d6cd58b7874015906b5d17bbe470a5";
        Map<String, String> headers = new HashMap();
        headers.put("Authorization", "APPCODE " + appcode);
        Map<String, Object> params = new HashMap<>();
        params.put("expressNo", no);
        params.put("mobile", mobile);

        try {
            HttpResponse response = HttpRequest.get(host + path).header("Authorization", "APPCODE " + appcode).form(params).execute();
            log.info(response.body());
            return response.body();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 定时随机增加商品销量
     * 每小时执行一次，随机为部分商品增加个位数的销量
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // 1小时 = 60 * 60 * 1000 毫秒
    public void scheduledSalesVolumeUpdate() {
        try {
            log.info("开始执行定时销量更新任务...");
            salesVolumeUpdateService.randomUpdateSalesVolume();
            log.info("定时销量更新任务执行完成");
        } catch (Exception e) {
            log.error("定时销量更新任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 定时更新已签收订单状态为已完成
     * 每天执行一次，将物流状态为SIGN且签收超过7天的订单状态更新为COMPLETED
     */
    @Scheduled(fixedRate = 24 * 60 * 60 * 1000) // 每天执行一次 = 24 * 60 * 60 * 1000 毫秒
    public void autoCompleteSignedOrders() {
        try {
            log.info("开始执行自动完成已签收订单任务...");

            // 获取所有物流信息
            List<OrderExpress> allExpressList = orderExpressService.queryAll();
            if (ListUtils.isBlank(allExpressList)) {
                log.info("没有找到任何物流信息");
                return;
            }

            // 计算7天前的时间
            Calendar sevenDaysAgo = Calendar.getInstance();
            sevenDaysAgo.add(Calendar.DAY_OF_MONTH, -7);
            Date sevenDaysAgoDate = sevenDaysAgo.getTime();

            int processedCount = 0;
            int successCount = 0;

            // 遍历所有物流信息，查找已签收且超过7天的订单
            for (OrderExpress express : allExpressList) {
                try {
                    // 检查物流状态是否为SIGN（已签收）
                    if (!"SIGN".equals(express.getLogisticsStatus())) {
                        continue;
                    }

                    // 检查更新时间是否超过7天
                    if (express.getUpdateTime() == null || express.getUpdateTime().after(sevenDaysAgoDate)) {
                        continue;
                    }

                    // 获取对应的订单
                    Order order = orderService.queryById(express.getOrderId());
                    if (order == null) {
                        log.warn("订单不存在，订单ID: {}", express.getOrderId());
                        continue;
                    }

                    // 检查订单状态是否已经是COMPLETED
                    if (order.getOrderStatus().getValue() == 3) {
                        log.debug("订单{}状态已经是COMPLETED，跳过", order.getId());
                        continue;
                    }

                    processedCount++;

                    // 调用确认订单方法，将订单状态更新为COMPLETED
                    Boolean result = orderService.confirm(order.getId());
                    if (result) {
                        successCount++;
                        log.info("订单{}状态已自动更新为COMPLETED，物流签收时间: {}",
                                order.getId(), DateUtil.formatDateTime(express.getUpdateTime()));
                    } else {
                        log.warn("订单{}状态更新失败", order.getId());
                    }

                } catch (Exception e) {
                    log.error("处理订单{}时发生异常: {}", express.getOrderId(), e.getMessage(), e);
                }
            }

            log.info("自动完成已签收订单任务执行完成，处理订单数: {}, 成功更新数: {}", processedCount, successCount);

        } catch (Exception e) {
            log.error("自动完成已签收订单任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 定时清理token黑名单
     * 每小时执行一次，清理过期的黑名单条目
     */
   /* @Scheduled(fixedRate = 60 * 60 * 1000) // 1小时 = 60 * 60 * 1000 毫秒
    public void cleanupTokenBlacklist() {
        try {
            log.debug("开始执行token黑名单清理任务...");
            tokenService.cleanupBlacklist();
            log.debug("token黑名单清理任务执行完成");
        } catch (Exception e) {
            log.error("token黑名单清理任务执行失败: {}", e.getMessage(), e);
        }
    }*/

    /**
     * 定时同步发货订单到微信
     * 每30分钟执行一次，将已发货但未同步到微信的订单信息上传到微信平台
     */
    @Scheduled(fixedRate = 30 *60 * 1000) // 30分钟 = 30 * 60 * 1000 毫秒
    public void syncShippingOrdersToWechat() {
        try {
            log.info("开始执行定时同步发货订单到微信任务...");

            // 查询所有已发货的订单快递信息
            OrderExpress build = OrderExpress.builder().build();
            List<OrderExpress> expressList = orderExpressService.queryList(build);
            if (ListUtils.isBlank(expressList)) {
                log.info("没有找到任何快递信息");
                return;
            }

            int processedCount = 0;
            int successCount = 0;
            int skipCount = 0;

            for (OrderExpress express : expressList) {
                try {
                    // 获取对应的订单信息
                    Order order = orderService.queryById(express.getOrderId());
                    if (order == null) {
                        log.warn("订单不存在，订单ID: {}", express.getOrderId());
                        continue;
                    }

                    // 检查订单状态是否为已发货或待收货
                    if (!OrderStatusEnum.WAIT_RECEIVE.equals(order.getOrderStatus()) &&
                        !OrderStatusEnum.COMPLETED.equals(order.getOrderStatus())) {
                        continue;
                    }

                    // 检查是否有微信支付单号
                    if (StringUtils.isBlank(order.getTransactionId())) {
                        log.debug("订单{}没有微信支付单号，跳过同步", order.getId());
                        continue;
                    }

                    // 检查是否已经同步过
                    WxShippingInfo existingInfo = wxShippingInfoService.queryByOrderId(order.getId());
                    if (existingInfo != null && existingInfo.getWxStatus() == 1) {
                        skipCount++;
                        continue; // 已经成功同步过，跳过
                    }

                    // 检查快递信息是否完整
                    if (StringUtils.isBlank(express.getLogisticCode()) ||
                        StringUtils.isBlank(express.getShipperCode())) {
                        log.debug("订单{}快递信息不完整，跳过同步", order.getId());
                        continue;
                    }

                    processedCount++;

                    // 构建微信发货信息DTO
                    WxShippingInfoDTO shippingInfoDTO = buildWxShippingInfoDTO(order, express);
                    if (shippingInfoDTO == null) {
                        log.warn("构建微信发货信息失败，订单ID: {}", order.getId());
                        continue;
                    }

                    // 调用微信发货信息上传接口
                    boolean uploadResult = wxOrderShippingService.uploadShippingInfo(shippingInfoDTO);
                    if (uploadResult) {
                        successCount++;
                        log.info("订单{}发货信息同步到微信成功", order.getId());
                    } else {
                        log.warn("订单{}发货信息同步到微信失败", order.getId());
                    }

                } catch (Exception e) {
                    log.error("处理订单{}发货信息同步时发生异常: {}", express.getOrderId(), e.getMessage(), e);
                }
            }

            log.info("定时同步发货订单到微信任务执行完成，处理订单数: {}, 成功同步数: {}, 跳过数: {}",
                    processedCount, successCount, skipCount);

        } catch (Exception e) {
            log.error("定时同步发货订单到微信任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 构建微信发货信息DTO
     * @param order 订单信息
     * @param express 快递信息
     * @return 微信发货信息DTO
     */
    private WxShippingInfoDTO buildWxShippingInfoDTO(Order order, OrderExpress express) {
        try {
            // 获取用户信息以获取openid
            User user = userService.queryById(order.getUserId());
            if (user == null || StringUtils.isBlank(user.getWechatOpenId())) {
                log.warn("订单{}对应的用户不存在或没有openid", order.getId());
                return null;
            }

            WxShippingInfoDTO dto = new WxShippingInfoDTO();

            // 设置订单信息
            WxShippingInfoDTO.OrderKey orderKey = new WxShippingInfoDTO.OrderKey();
            orderKey.setOrderNumberType(2); // 使用微信支付单号
            orderKey.setTransactionId(order.getTransactionId());
            orderKey.setMchid(order.getMchid());
            orderKey.setOutTradeNo(order.getOrderSn());
            dto.setOrderKey(orderKey);

            // 设置物流模式：1-实体物流配送
            dto.setLogisticsType(1);

            // 设置发货模式：1-统一发货
            dto.setDeliveryMode(1);

            // 设置物流信息
            List<WxShippingInfoDTO.ShippingItem> shippingList = new ArrayList<>();
            WxShippingInfoDTO.ShippingItem shippingItem = new WxShippingInfoDTO.ShippingItem();
            shippingItem.setTrackingNo(express.getLogisticCode());
            shippingItem.setExpressCompany(express.getShipperCode());

            // 构建商品描述
            String itemDesc = buildItemDescription(order);
            shippingItem.setItemDesc(itemDesc);

            // 如果是顺丰快递，需要设置联系方式
            if ("SF".equalsIgnoreCase(express.getShipperCode())) {
                WxShippingInfoDTO.Contact contact = new WxShippingInfoDTO.Contact();
                // 对手机号进行掩码处理（保留后4位）
                String maskedPhone = maskPhoneNumber(order.getMobile());
                contact.setReceiverContact(maskedPhone);
                shippingItem.setContact(contact);
            }

            shippingList.add(shippingItem);
            dto.setShippingList(shippingList);

            // 设置支付者信息
            WxShippingInfoDTO.Payer payer = new WxShippingInfoDTO.Payer();
            payer.setOpenid(user.getWechatOpenId());
            dto.setPayer(payer);

            return dto;

        } catch (Exception e) {
            log.error("构建微信发货信息DTO失败，订单ID: {}", order.getId(), e);
            return null;
        }
    }

    /**
     * 构建商品描述信息
     * @param order 订单信息
     * @return 商品描述
     */
    private String buildItemDescription(Order order) {
        try {
            // 这里可以根据实际需求构建商品描述
            // 可以查询订单商品详情，拼接商品名称等
            return "订单商品 - 订单号: " + order.getOrderSn();
        } catch (Exception e) {
            log.error("构建商品描述失败，订单ID: {}", order.getId(), e);
            return "订单商品";
        }
    }

    /**
     * 对手机号进行掩码处理
     * @param phoneNumber 原始手机号
     * @return 掩码后的手机号
     */
    private String maskPhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber) || phoneNumber.length() < 4) {
            return phoneNumber;
        }

        // 保留后4位，其他位用*替换
        int length = phoneNumber.length();
        StringBuilder masked = new StringBuilder();
        for (int i = 0; i < length - 4; i++) {
            masked.append("*");
        }
        masked.append(phoneNumber.substring(length - 4));

        return masked.toString();
    }

}

@Data
@AllArgsConstructor
class Trace {
    private String content;
    private String datetime;
    private String status;
}
