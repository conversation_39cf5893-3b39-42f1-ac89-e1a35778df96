package com.logic.code.model.vo;

import com.logic.code.entity.User;

import java.util.List;

/**
 * 管理员用户列表返回对象
 * <AUTHOR>
 * @date 2025/1/27
 */
public class AdminUserListVO {
    
    private List<AdminUserVO> list;
    private Integer pageNum;
    private Integer pageSize;
    private Integer pages;
    private Long total;

    public List<AdminUserVO> getList() {
        return list;
    }

    public void setList(List<AdminUserVO> list) {
        this.list = list;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    /**
     * 管理员用户信息VO
     */
    public static class AdminUserVO {
        private Long id;
        private String nickname;
        private String username;
        private String mobile;
        private String avatar;
        private Integer userLevelId;
        private String userLevelText;
        private String registerTime;
        private String lastLoginTime;
        private Boolean isOnline;
        private Integer orderCount;
        private String totalAmount;
        private Integer points;
        private String balance;
        private Integer promotionCount;
        private Long promoterId;
        private String promoterName;
        private String promotionEarnings;
        private String pendingEarnings; // 待确认收益
        private String withdrawableAmount; // 可提现金额

        // Getters and Setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getMobile() {
            return mobile;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public Integer getUserLevelId() {
            return userLevelId;
        }

        public void setUserLevelId(Integer userLevelId) {
            this.userLevelId = userLevelId;
        }

        public String getUserLevelText() {
            return userLevelText;
        }

        public void setUserLevelText(String userLevelText) {
            this.userLevelText = userLevelText;
        }

        public String getRegisterTime() {
            return registerTime;
        }

        public void setRegisterTime(String registerTime) {
            this.registerTime = registerTime;
        }

        public String getLastLoginTime() {
            return lastLoginTime;
        }

        public void setLastLoginTime(String lastLoginTime) {
            this.lastLoginTime = lastLoginTime;
        }

        public Boolean getIsOnline() {
            return isOnline;
        }

        public void setIsOnline(Boolean isOnline) {
            this.isOnline = isOnline;
        }

        public Integer getOrderCount() {
            return orderCount;
        }

        public void setOrderCount(Integer orderCount) {
            this.orderCount = orderCount;
        }

        public String getTotalAmount() {
            return totalAmount;
        }

        public void setTotalAmount(String totalAmount) {
            this.totalAmount = totalAmount;
        }

        public Integer getPoints() {
            return points;
        }

        public void setPoints(Integer points) {
            this.points = points;
        }

        public String getBalance() {
            return balance;
        }

        public void setBalance(String balance) {
            this.balance = balance;
        }

        public Integer getPromotionCount() {
            return promotionCount;
        }

        public void setPromotionCount(Integer promotionCount) {
            this.promotionCount = promotionCount;
        }

        public Long getPromoterId() {
            return promoterId;
        }

        public void setPromoterId(Long promoterId) {
            this.promoterId = promoterId;
        }

        public String getPromoterName() {
            return promoterName;
        }

        public void setPromoterName(String promoterName) {
            this.promoterName = promoterName;
        }

        public String getPromotionEarnings() {
            return promotionEarnings;
        }

        public void setPromotionEarnings(String promotionEarnings) {
            this.promotionEarnings = promotionEarnings;
        }

        public String getPendingEarnings() {
            return pendingEarnings;
        }

        public void setPendingEarnings(String pendingEarnings) {
            this.pendingEarnings = pendingEarnings;
        }

        public String getWithdrawableAmount() {
            return withdrawableAmount;
        }

        public void setWithdrawableAmount(String withdrawableAmount) {
            this.withdrawableAmount = withdrawableAmount;
        }
    }
}