package com.logic.code.entity.order;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName( "weshop_shipper")
@Data
public class Shipper {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 快递公司名称
     */
    private String name;

    /**
     * 快递公司代码
     */
    private String code;

    /**
     * 排序
     */
    private Integer sortOrder;

}
