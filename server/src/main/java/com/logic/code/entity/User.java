package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.logic.code.common.enmus.GenderEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.units.qual.A;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/7 14:30
 * @desc
 */
@TableName("weshop_user")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class User {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String username;

    private String password;

    private Integer gender;

    private Date birthday;

    private Date registerTime;

    private Date lastLoginTime;

    private String lastLoginIp;

    private Integer userLevelId;

    private String nickname;

    private String mobile;

    private String registerIp;

    private String avatar;

    private String wechatOpenId;

    /**
     * 推广者用户ID，记录是谁推广了这个用户
     */
    private Integer promoterId;

    /**
     * 用户专属推广码，格式：promo_用户ID
     */
    private String promotionCode;

    /**
     * 推广用户数量统计
     */
    private Integer promotionCount;

    /**
     * 成为推广用户的时间（被推广的时间）
     */
    private Date promotionTime;

    /**
     * 首次进行推广的时间
     */
    private Date firstPromotionTime;

    /**
     * 推广等级，0-普通用户，1-推广员，2-高级推广员
     */
    private Integer promotionLevel;

    /**
     * 用户余额
     */
    private BigDecimal balance;

    /**
     * 用户积分
     */
    private Integer points;

    private Date updateTime;


}
