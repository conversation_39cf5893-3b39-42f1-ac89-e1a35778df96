package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 推广收益记录实体
 */
@Data
@TableName("weshop_promotion_earnings")
public class PromotionEarnings {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 推广者用户ID
     */
    private Integer promoterId;
    
    /**
     * 被推广用户ID
     */
    private Integer promotedUserId;
    
    /**
     * 商品ID
     */
    private Integer goodsId;
    
    /**
     * 商品名称
     */
    private String goodsName;
    
    /**
     * 推广该商品的第几笔订单
     */
    private Integer promotionOrderCount;
    
    /**
     * 是否为阶梯推广商品：0-否，1-是
     */
    private Boolean isTieredPromotion;
    
    /**
     * 订单ID
     */
    private Integer orderId;
    
    /**
     * 订单编号
     */
    private String orderNo;
    
    /**
     * 订单金额
     */
    private BigDecimal orderAmount;
    
    /**
     * 佣金比例（百分比，如10表示10%）
     */
    private BigDecimal commissionRate;
    
    /**
     * 佣金金额
     */
    private BigDecimal commissionAmount;
    
    /**
     * 收益状态：pending-待确认，confirmed-已确认，cancelled-已取消
     */
    private String status;
    
    /**
     * 订单创建时间
     */
    private Date orderCreateTime;
    
    /**
     * 确认收货时间
     */
    private Date confirmTime;
    
    /**
     * 收益生效时间
     */
    private Date effectiveTime;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 描述信息
     */
    private String description;
    
    /**
     * 是否已提现：0-未提现，1-已提现
     */
    private Byte isWithdrawn;
    
    /**
     * 提现时间
     */
    private Date withdrawTime;
}
