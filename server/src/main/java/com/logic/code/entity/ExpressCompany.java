package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 快递公司实体类 - 对应weshop_shipper表
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("weshop_shipper")
public class ExpressCompany {
    
    /**
     * 快递公司ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 快递公司名称
     */
    private String shipperName;
    
    /**
     * 快递公司编码
     */
    private String shipperCode;
    
    /**
     * 快递公司简称
     */
    private String shortName;
    
    /**
     * 是否启用 1-启用 0-禁用
     */
    private Integer enabled;
    
    /**
     * 排序
     */
    private Integer sort;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private java.util.Date addTime;
    
    /**
     * 更新时间
     */
    private java.util.Date updateTime;
}