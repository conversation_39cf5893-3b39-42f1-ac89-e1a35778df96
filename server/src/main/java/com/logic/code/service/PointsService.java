package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.common.enmus.OrderStatusEnum;
import com.logic.code.entity.PointsConfig;
import com.logic.code.entity.PointsRecord;
import com.logic.code.entity.User;
import com.logic.code.entity.order.Order;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.OrderMapper;
import com.logic.code.mapper.PointsConfigMapper;
import com.logic.code.mapper.PointsRecordMapper;
import com.logic.code.mapper.UserMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 积分服务类
 */
@Service
@Slf4j
public class PointsService extends BaseService<PointsRecord> {
    
    @Resource
    private PointsRecordMapper pointsRecordMapper;
    
    @Resource
    private PointsConfigMapper pointsConfigMapper;
    
    @Resource
    private UserMapper userMapper;

    @Resource
    private OrderMapper orderMapper;
    
    @Override
    protected CommonMapper<PointsRecord> getMapper() {
        return pointsRecordMapper;
    }

    /**
     * 获取积分记录Mapper（供其他服务使用）
     */
    public PointsRecordMapper getPointsRecordMapper() {
        return pointsRecordMapper;
    }
    
    /**
     * 获取积分配置
     */
    public PointsConfig getPointsConfig() {
        PointsConfig config = pointsConfigMapper.getCurrentConfig();
        if (config == null) {
            // 返回默认配置
            config = new PointsConfig()
                    .setEarnRate(new BigDecimal("1.00"))
                    .setUseRate(new BigDecimal("100.00"))
                    .setMinUsePoints(100)
                    .setMaxUseRatio(new BigDecimal("100.00")) // 修改为100%，允许全额抵扣
                    .setIsEnabled(true);
        }
        return config;
    }
    
    /**
     * 根据订单金额计算获得的积分
     */
    public Integer calculateEarnPoints(BigDecimal orderAmount) {
        PointsConfig config = getPointsConfig();
        if (!config.getIsEnabled()) {
            return 0;
        }
        
        // 每消费earnRate元获得1积分
        return orderAmount.divide(config.getEarnRate(), 0, RoundingMode.DOWN).intValue();
    }
    
    /**
     * 计算积分可以抵扣的金额
     */
    public BigDecimal calculatePointsValue(Integer points) {
        PointsConfig config = getPointsConfig();
        if (!config.getIsEnabled() || points == null || points <= 0) {
            return BigDecimal.ZERO;
        }
        
        // useRate积分抵扣1元
        return new BigDecimal(points).divide(config.getUseRate(), 2, RoundingMode.DOWN);
    }
    
    /**
     * 计算可以使用的最大积分数量
     */
    public Integer calculateMaxUsablePoints(Integer userPoints, BigDecimal orderAmount) {
        PointsConfig config = getPointsConfig();
        if (!config.getIsEnabled() || userPoints == null || userPoints <= 0) {
            return 0;
        }
        
        // 不能少于最小使用积分
        if (userPoints < config.getMinUsePoints()) {
            return 0;
        }
        
        // 计算订单金额允许的最大抵扣金额
        BigDecimal maxDeductAmount = orderAmount.multiply(config.getMaxUseRatio()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN);
        
        // 计算最大可用积分数量
        Integer maxPointsByAmount = maxDeductAmount.multiply(config.getUseRate()).intValue();
        
        // 返回用户积分和订单限制中的较小值
        return Math.min(userPoints, maxPointsByAmount);
    }
    
    /**
     * 订单完成后增加积分（优化版本，增加幂等性检查）
     */
    @Transactional
    public boolean earnPointsFromOrder(Integer userId, Integer orderId, BigDecimal orderAmount) {
        try {
            // 幂等性检查：避免重复奖励积分
            QueryWrapper<PointsRecord> existWrapper = new QueryWrapper<>();
            existWrapper.eq("user_id", userId)
                       .eq("source_id", orderId)
                       .eq("source", "order")
                       .eq("type", "earn");

            long existCount = pointsRecordMapper.selectCount(existWrapper);
            if (existCount > 0) {
                log.warn("订单{}已经处理过积分奖励，跳过重复处理", orderId);
                return true;
            }

            Integer earnPoints = calculateEarnPoints(orderAmount);
            if (earnPoints <= 0) {
                log.debug("订单{}金额{}无积分可获得", orderId, orderAmount);
                return true; // 没有积分可获得，但不算失败
            }

            // 创建积分记录
            PointsRecord record = new PointsRecord()
                    .setUserId(userId)
                    .setType("earn")
                    .setPoints(earnPoints)
                    .setSource("order")
                    .setSourceId(orderId)
                    .setDescription("订单消费获得积分")
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date());

            int insertResult = pointsRecordMapper.insert(record);
            if (insertResult <= 0) {
                log.error("积分记录插入失败，userId: {}, orderId: {}", userId, orderId);
                return false;
            }

            // 更新用户积分
            updateUserPoints(userId);

            log.info("用户{}通过订单{}获得{}积分", userId, orderId, earnPoints);
            return true;
        } catch (Exception e) {
            log.error("用户{}订单{}积分增加失败", userId, orderId, e);
            return false;
        }
    }
    
    /**
     * 使用积分抵扣（改进版本，使用原子性操作）
     */

    public boolean usePointsForOrder(Integer userId, Integer orderId, Integer usePoints, String description) {
        try {
            if (usePoints <= 0) {
                return true;
            }

            // 使用原子性操作扣减积分，防止并发问题
            int affectedRows = userMapper.deductUserPoints(userId, usePoints);
            if (affectedRows == 0) {
                // 查询用户当前积分用于错误信息
                User user = userMapper.selectById(userId);
                Integer currentPoints = user != null && user.getPoints() != null ? user.getPoints() : 0;
                log.warn("用户{}积分扣减失败，当前积分：{}，尝试使用：{}", userId, currentPoints, usePoints);
                throw new RuntimeException("积分不足，当前积分：" + currentPoints + "，尝试使用：" + usePoints);
            }

            // 创建积分使用记录
            PointsRecord record = new PointsRecord()
                    .setUserId(userId)
                    .setType("use")
                    .setPoints(-usePoints) // 负数表示使用
                    .setSource("order")
                    .setSourceId(orderId)
                    .setDescription(description != null ? description : "订单使用积分抵扣")
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date());

            pointsRecordMapper.insert(record);

            // 获取扣减后的积分余额
            User updatedUser = userMapper.selectById(userId);
            log.info("用户{}在订单{}中使用{}积分，剩余积分：{}", userId, orderId, usePoints, updatedUser.getPoints());
            return true;
        } catch (Exception e) {
            log.error("用户{}订单{}积分使用失败：{}", userId, orderId, e.getMessage(), e);
            throw new RuntimeException("积分使用失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 更新用户积分总数（优化版本，增加数据一致性保障）
     */
    @Transactional
    public void updateUserPoints(Integer userId) {
        try {
            log.debug("开始更新用户{}的积分总数", userId);

            // 获取用户当前积分
            User currentUser = userMapper.selectById(userId);
            if (currentUser == null) {
                log.error("用户{}不存在，无法更新积分", userId);
                return;
            }

            Integer currentPoints = currentUser.getPoints() != null ? currentUser.getPoints() : 0;

            // 从积分记录表计算总积分
            Integer totalPoints = pointsRecordMapper.getUserTotalPoints(userId);
            if (totalPoints == null) {
                totalPoints = 0;
            }

            log.debug("用户{}从积分记录计算出的总积分：{}，当前用户表中的积分：{}", userId, totalPoints, currentPoints);

            // 确保积分不会变成负数
            if (totalPoints < 0) {
                log.error("用户{}计算出的积分总数为负数：{}，设置为0", userId, totalPoints);
                totalPoints = 0;
            }

            // 只有当积分发生变化时才更新
            if (!totalPoints.equals(currentPoints)) {
                User user = new User();
                user.setId(userId);
                user.setPoints(totalPoints);
                user.setUpdateTime(new Date());
                int updateResult = userMapper.updateById(user);

                if (updateResult > 0) {
                    log.info("用户{}积分更新成功：{} -> {}，差值：{}", userId, currentPoints, totalPoints, totalPoints - currentPoints);
                } else {
                    log.error("用户{}积分更新失败，数据库更新返回0行", userId);
                    throw new RuntimeException("积分更新失败，数据库操作异常");
                }
            } else {
                log.debug("用户{}积分无变化，跳过更新", userId);
            }
        } catch (Exception e) {
            log.error("更新用户{}积分总数失败", userId, e);
            throw new RuntimeException("更新用户积分失败：" + e.getMessage(), e);
        }
    }

    /**
     * 原子性地扣减用户积分（使用数据库级别的操作）
     */
    @Transactional
    public boolean atomicDeductPoints(Integer userId, Integer deductPoints) {
        try {
            // 使用SQL直接更新，确保原子性
            int affectedRows = userMapper.deductUserPoints(userId, deductPoints);
            return affectedRows > 0;
        } catch (Exception e) {
            log.error("原子性扣减用户{}积分{}失败", userId, deductPoints, e);
            return false;
        }
    }
    
    /**
     * 获取用户积分信息
     */
    public Map<String, Object> getUserPointsInfo(Integer userId) {
        User user = userMapper.selectById(userId);
        PointsConfig config = getPointsConfig();
        
        Map<String, Object> result = new HashMap<>();
        result.put("points", user.getPoints() != null ? user.getPoints() : 0);
        result.put("pointsConfig", config);
        result.put("minUsePoints", config.getMinUsePoints());
        result.put("useRate", config.getUseRate());
        result.put("earnRate", config.getEarnRate());
        
        return result;
    }
    
    /**
     * 获取用户积分记录
     */
    public Map<String, Object> getUserPointsRecords(Integer userId, Integer page, Integer size) {
        if (page == null || page < 1) page = 1;
        if (size == null || size < 1) size = 20;
        
        Integer offset = (page - 1) * size;
        List<PointsRecord> records = pointsRecordMapper.getUserPointsRecords(userId, offset, size);
        Integer total = pointsRecordMapper.getUserPointsRecordsCount(userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", records);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (total + size - 1) / size);
        
        return result;
    }
    
    /**
     * 手动调整用户积分（管理员功能）
     */
    @Transactional
    public boolean adjustUserPoints(Integer userId, Integer points, String description) {
        try {
            PointsRecord record = new PointsRecord()
                    .setUserId(userId)
                    .setType(points > 0 ? "earn" : "use")
                    .setPoints(points)
                    .setSource("manual")
                    .setDescription(description != null ? description : "管理员手动调整")
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date());

            pointsRecordMapper.insert(record);
            updateUserPoints(userId);

            log.info("管理员调整用户{}积分：{}", userId, points);
            return true;
        } catch (Exception e) {
            log.error("调整用户{}积分失败", userId, e);
            return false;
        }
    }

    /**
     * 退回积分（订单取消时调用）
     */
    @Transactional
    public boolean refundPointsFromOrder(Integer userId, Integer orderId) {
        try {
            log.info("开始处理用户{}订单{}的积分退回", userId, orderId);

            // 查找该订单使用的积分记录
            QueryWrapper<PointsRecord> wrapper = new QueryWrapper<>();
            wrapper.eq("user_id", userId)
                   .eq("source_id", orderId)
                   .eq("source", "order")
                   .eq("type", "use");

            PointsRecord useRecord = pointsRecordMapper.selectOne(wrapper);
            log.info("查找到的积分使用记录：{}", useRecord);

            if (useRecord != null && useRecord.getPoints() < 0) {
                // 检查是否已经退回过积分
                QueryWrapper<PointsRecord> refundWrapper = new QueryWrapper<>();
                refundWrapper.eq("user_id", userId)
                           .eq("source_id", orderId)
                           .eq("source", "order_cancel")
                           .eq("type", "refund");

                PointsRecord existingRefund = pointsRecordMapper.selectOne(refundWrapper);
                if (existingRefund != null) {
                    log.info("用户{}订单{}的积分已经退回过了，退回记录ID：{}", userId, orderId, existingRefund.getId());
                    return true;
                }

                // 创建退回积分记录
                Integer refundPoints = Math.abs(useRecord.getPoints());
                PointsRecord refundRecord = new PointsRecord()
                        .setUserId(userId)
                        .setType("refund")
                        .setPoints(refundPoints)
                        .setSource("order_cancel")
                        .setSourceId(orderId)
                        .setDescription("订单取消退回积分")
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());

                int insertResult = pointsRecordMapper.insert(refundRecord);
                log.info("积分退回记录插入结果：{}, 记录ID：{}", insertResult, refundRecord.getId());

                // 获取用户当前积分（更新前）
                User userBefore = userMapper.selectById(userId);
                Integer pointsBefore = userBefore != null && userBefore.getPoints() != null ? userBefore.getPoints() : 0;

                // 更新用户积分总数（修复：使用统一的更新方法）
                updateUserPoints(userId);

                // 获取用户更新后积分
                User userAfter = userMapper.selectById(userId);
                Integer pointsAfter = userAfter != null && userAfter.getPoints() != null ? userAfter.getPoints() : 0;

                log.info("用户{}订单{}取消，退回{}积分，积分变化：{} -> {}", userId, orderId, refundPoints, pointsBefore, pointsAfter);
                return true;
            } else {
                log.info("用户{}订单{}没有找到积分使用记录或积分为正数，无需退回", userId, orderId);
                return true; // 没有使用积分，不算失败
            }
        } catch (Exception e) {
            log.error("用户{}订单{}积分退回失败", userId, orderId, e);
            return false;
        }
    }

    /**
     * 修复历史数据中积分退回问题
     * 查找所有已取消但未退回积分的订单，并补充退回记录
     */
    @Transactional
    public void fixHistoricalPointsRefundIssues() {
        try {
            log.info("开始修复历史积分退回问题...");

            // 查找所有使用了积分但已取消且未退回积分的订单
            List<Order> problematicOrders = orderMapper.selectList(
                new QueryWrapper<Order>()
                    .gt("integral", 0)
                    .eq("order_status", OrderStatusEnum.CANCELLED.getValue()) // 已取消状态
            );

            int fixedCount = 0;
            int skippedCount = 0;

            for (Order order : problematicOrders) {
                // 检查是否已经有退回记录
                QueryWrapper<PointsRecord> refundWrapper = new QueryWrapper<>();
                refundWrapper.eq("user_id", order.getUserId())
                           .eq("source_id", order.getId())
                           .eq("source", "order_cancel")
                           .eq("type", "refund");

                PointsRecord existingRefund = pointsRecordMapper.selectOne(refundWrapper);
                if (existingRefund != null) {
                    log.debug("订单{}已有退回记录，跳过", order.getId());
                    skippedCount++;
                    continue;
                }

                // 查找原始使用记录
                QueryWrapper<PointsRecord> useWrapper = new QueryWrapper<>();
                useWrapper.eq("user_id", order.getUserId())
                         .eq("source_id", order.getId())
                         .eq("source", "order")
                         .eq("type", "use");

                PointsRecord useRecord = pointsRecordMapper.selectOne(useWrapper);
                if (useRecord == null || useRecord.getPoints() >= 0) {
                    log.warn("订单{}未找到有效的积分使用记录", order.getId());
                    continue;
                }

                // 创建退回记录
                Integer refundPoints = Math.abs(useRecord.getPoints());
                PointsRecord refundRecord = new PointsRecord()
                        .setUserId(order.getUserId())
                        .setType("refund")
                        .setPoints(refundPoints)
                        .setSource("order_cancel")
                        .setSourceId(order.getId())
                        .setDescription("订单取消退回积分-历史数据修复")
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date());

                pointsRecordMapper.insert(refundRecord);

                // 更新用户积分
                updateUserPoints(order.getUserId());

                log.info("修复订单{}的积分退回，用户{}退回{}积分",
                    order.getId(), order.getUserId(), refundPoints);
                fixedCount++;
            }

            log.info("历史积分退回问题修复完成，修复{}个订单，跳过{}个订单", fixedCount, skippedCount);

        } catch (Exception e) {
            log.error("修复历史积分退回问题失败", e);
            throw new RuntimeException("修复历史积分退回问题失败：" + e.getMessage(), e);
        }
    }

    /**
     * 验证用户积分数据一致性（优化版本）
     * 检查用户表中的积分是否与积分记录表计算出的总积分一致
     */
    public Map<String, Object> validateUserPointsConsistency() {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> inconsistentUsers = new ArrayList<>();

        try {
            log.info("开始验证用户积分数据一致性...");

            List<User> users = userMapper.selectList(new QueryWrapper<User>().isNotNull("points"));
            int totalUsers = users.size();
            int inconsistentCount = 0;
            int fixedCount = 0;

            for (User user : users) {
                Integer userTablePoints = user.getPoints() != null ? user.getPoints() : 0;
                Integer calculatedPoints = pointsRecordMapper.getUserTotalPoints(user.getId());
                if (calculatedPoints == null) {
                    calculatedPoints = 0;
                }

                if (!userTablePoints.equals(calculatedPoints)) {
                    Map<String, Object> inconsistentUser = new HashMap<>();
                    inconsistentUser.put("userId", user.getId());
                    inconsistentUser.put("userTablePoints", userTablePoints);
                    inconsistentUser.put("calculatedPoints", calculatedPoints);
                    inconsistentUser.put("difference", calculatedPoints - userTablePoints);
                    inconsistentUsers.add(inconsistentUser);

                    log.warn("用户{}积分不一致：用户表={}, 计算值={}, 差值={}",
                        user.getId(), userTablePoints, calculatedPoints, calculatedPoints - userTablePoints);
                    inconsistentCount++;
                }
            }

            result.put("totalUsers", totalUsers);
            result.put("inconsistentCount", inconsistentCount);
            result.put("inconsistentUsers", inconsistentUsers);
            result.put("checkTime", new Date());

            if (inconsistentCount == 0) {
                log.info("✓ 所有{}个用户积分数据一致", totalUsers);
                result.put("status", "consistent");
                result.put("message", "所有用户积分数据一致");
            } else {
                log.warn("✗ 发现{}个用户积分数据不一致，总用户数：{}", inconsistentCount, totalUsers);
                result.put("status", "inconsistent");
                result.put("message", String.format("发现%d个用户积分数据不一致", inconsistentCount));
            }

        } catch (Exception e) {
            log.error("验证用户积分数据一致性失败", e);
            result.put("status", "error");
            result.put("message", "验证失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 批量修复用户积分数据不一致问题
     * @param autoFix 是否自动修复
     * @return 修复结果
     */
    @Transactional
    public Map<String, Object> fixUserPointsInconsistency(boolean autoFix) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> fixedUsers = new ArrayList<>();

        try {
            log.info("开始批量修复用户积分数据不一致问题，自动修复：{}", autoFix);

            List<User> users = userMapper.selectList(new QueryWrapper<User>().isNotNull("points"));
            int totalUsers = users.size();
            int inconsistentCount = 0;
            int fixedCount = 0;
            int failedCount = 0;

            for (User user : users) {
                Integer userTablePoints = user.getPoints() != null ? user.getPoints() : 0;
                Integer calculatedPoints = pointsRecordMapper.getUserTotalPoints(user.getId());
                if (calculatedPoints == null) {
                    calculatedPoints = 0;
                }

                if (!userTablePoints.equals(calculatedPoints)) {
                    inconsistentCount++;

                    if (autoFix) {
                        try {
                            // 确保积分不为负数
                            if (calculatedPoints < 0) {
                                log.warn("用户{}计算出的积分为负数：{}，设置为0", user.getId(), calculatedPoints);
                                calculatedPoints = 0;
                            }

                            User updateUser = new User();
                            updateUser.setId(user.getId());
                            updateUser.setPoints(calculatedPoints);
                            updateUser.setUpdateTime(new Date());

                            int updateResult = userMapper.updateById(updateUser);
                            if (updateResult > 0) {
                                Map<String, Object> fixedUser = new HashMap<>();
                                fixedUser.put("userId", user.getId());
                                fixedUser.put("beforePoints", userTablePoints);
                                fixedUser.put("afterPoints", calculatedPoints);
                                fixedUser.put("difference", calculatedPoints - userTablePoints);
                                fixedUser.put("fixTime", new Date());
                                fixedUsers.add(fixedUser);

                                fixedCount++;
                                log.info("修复用户{}积分：{} -> {}，差值：{}",
                                    user.getId(), userTablePoints, calculatedPoints, calculatedPoints - userTablePoints);
                            } else {
                                failedCount++;
                                log.error("修复用户{}积分失败，数据库更新返回0行", user.getId());
                            }
                        } catch (Exception e) {
                            failedCount++;
                            log.error("修复用户{}积分失败", user.getId(), e);
                        }
                    }
                }
            }

            result.put("totalUsers", totalUsers);
            result.put("inconsistentCount", inconsistentCount);
            result.put("fixedCount", fixedCount);
            result.put("failedCount", failedCount);
            result.put("fixedUsers", fixedUsers);
            result.put("autoFix", autoFix);
            result.put("fixTime", new Date());

            if (autoFix) {
                log.info("批量修复完成：总用户数={}, 不一致数={}, 修复成功={}, 修复失败={}",
                    totalUsers, inconsistentCount, fixedCount, failedCount);
                result.put("message", String.format("修复完成：%d个用户中发现%d个不一致，成功修复%d个，失败%d个",
                    totalUsers, inconsistentCount, fixedCount, failedCount));
            } else {
                log.info("检查完成：总用户数={}, 不一致数={}", totalUsers, inconsistentCount);
                result.put("message", String.format("检查完成：%d个用户中发现%d个积分不一致", totalUsers, inconsistentCount));
            }

        } catch (Exception e) {
            log.error("批量修复用户积分数据失败", e);
            result.put("status", "error");
            result.put("message", "修复失败：" + e.getMessage());
        }

        return result;
    }
}