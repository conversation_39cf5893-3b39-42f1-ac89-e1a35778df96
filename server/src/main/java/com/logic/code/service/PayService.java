package com.logic.code.service;

import com.github.binarywang.wxpay.bean.coupon.*;
import com.github.binarywang.wxpay.bean.notify.*;
import com.github.binarywang.wxpay.bean.request.*;
import com.github.binarywang.wxpay.bean.result.*;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.logic.code.common.constants.WechatConstants;
import com.logic.code.common.enmus.OrderStatusEnum;
import com.logic.code.common.enmus.PayStatusEnum;
import com.logic.code.common.enmus.WechatPayResultStatus;
import com.logic.code.common.enmus.WeshopWechatResultStatus;
import com.logic.code.common.exception.WeshopPayException;
import com.logic.code.common.exception.WeshopWechatException;
import com.logic.code.common.response.Result;
import com.logic.code.common.utils.StringUtils;
import com.logic.code.entity.User;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.PointsRecord;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.File;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/5/8 11:05
 * @desc
 */
@Service
@Slf4j
public class PayService {

    @Autowired
    private OrderService orderService;

    @Autowired
    private UserService userService;
    
    @Autowired
    private PointsService pointsService;
    
    @Autowired
    private OrderGoodsService orderGoodsService;
    
    @Autowired
    private PaymentEventHandler paymentEventHandler;

    @Value("${weshop.wx.miniapp.appid}")
    private String appid;


    public WxPayUnifiedOrderV3Result.JsapiResult prepay(Integer orderId) {
        Order order = orderService.queryById(orderId);
        if (order == null) {
            //订单已取消
            throw new WeshopWechatException(WeshopWechatResultStatus.ORDER_CANCELED);
        }
        if (order.getPayStatus() == PayStatusEnum.PAID) {
            throw new WeshopWechatException(WeshopWechatResultStatus.ORDER_PAID);
        }
        
        // 检查实际支付金额，如果为0则直接完成订单
        if (order.getActualPrice().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("订单{}实际支付金额为0，直接完成订单", orderId);
            // 直接更新订单状态为已支付，并设置支付时间
            Date payTime = new Date(); // 当前时间作为支付时间
            orderService.updateNotNull(order
                .setOrderStatus(OrderStatusEnum.WAIT_SEND)
                .setPayStatus(PayStatusEnum.PAID)
                .setPayTime(payTime));  // 更新支付时间
            log.info("0元订单支付完成，订单ID：{}，支付时间：{}", orderId, payTime);
            // 返回空的支付参数，前端会跳过微信支付
            return null;
        }
        
        String wechatOpenId = Result.success(userService.queryOneByCriteria(Criteria.of(User.class).fields(User::getWechatOpenId).andEqualTo(User::getId, order.getUserId())))
                .orElseGetData(() -> new User()).getWechatOpenId();
        //不存在openid，说明不是微信下的单
        if (StringUtils.isBlank(wechatOpenId)) {
            throw new WeshopWechatException(WeshopWechatResultStatus.WECHAT_PAY_FAIL);
        }
        WxPayUnifiedOrderV3Request wxPayUnifiedOrderV3Request = new WxPayUnifiedOrderV3Request();
        wxPayUnifiedOrderV3Request.setAppid("wxca19c2771bc2ea41");
        wxPayUnifiedOrderV3Request.setMchid("1717878370");
        String orderSn = order.getOrderSn();
        wxPayUnifiedOrderV3Request.setOutTradeNo(orderSn);
        wxPayUnifiedOrderV3Request.setDescription("订单编号：" + order.getOrderSn());
        wxPayUnifiedOrderV3Request.setNotifyUrl("https://www.sxwjsm.com/weshop-wjhx/wechat/pay/notify");
        // 支付金额信息
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        // 获取应付金额
        //amount.setTotal(1);
        amount.setTotal(order.getActualPrice().multiply(new BigDecimal(100)).intValue());
        amount.setCurrency("CNY");
        wxPayUnifiedOrderV3Request.setAmount(amount);
        // 支付用户信息
        WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
        payer.setOpenid(wechatOpenId);
        wxPayUnifiedOrderV3Request.setPayer(payer);

        Result<WxPayUnifiedOrderV3Result.JsapiResult> result = createOrder(wxPayUnifiedOrderV3Request);
        if (!result.isSuccess()) {
            throw new WeshopWechatException(WeshopWechatResultStatus.WECHAT_PAY_FAIL);
        }
        return result.getData();
    }

    public WxPayRefundV3Result refundV3(Integer orderId) {
        Order order = orderService.queryById(orderId);
        if (order == null) {
            //订单已取消
            throw new WeshopWechatException(WeshopWechatResultStatus.ORDER_CANCELED);
        }
        if (order.getPayStatus() != PayStatusEnum.PAID) {
            throw new WeshopWechatException(WeshopWechatResultStatus.WECHAT_PAY_FAIL);
        }
        String wechatOpenId = Result.success(userService.queryOneByCriteria(Criteria.of(User.class).fields(User::getWechatOpenId).andEqualTo(User::getId, order.getUserId())))
                .orElseGetData(() -> new User()).getWechatOpenId();
        if (StringUtils.isBlank(wechatOpenId)) {
            throw new WeshopWechatException(WeshopWechatResultStatus.WECHAT_PAY_FAIL);
        }
        WxPayRefundV3Request refundV3Request = new WxPayRefundV3Request();
        String orderSn = order.getOrderSn();
        refundV3Request.setOutTradeNo("4499d18c0-202505311105325334096");
        refundV3Request.setOutRefundNo("4499d18c0-202505311105325334096");
        refundV3Request.setNotifyUrl("https://www.sxwjsm.com/weshop-wjhx/wechat/pay/notifyRefund");
        // 支付金额信息
        WxPayRefundV3Request.Amount amount = new WxPayRefundV3Request.Amount();
        // 获取应付金额
        //amount.setTotal(1);
        amount.setTotal(order.getActualPrice().multiply(new BigDecimal(100)).intValue());
        amount.setRefund(order.getActualPrice().multiply(new BigDecimal(100)).intValue());
        amount.setCurrency("CNY");
        refundV3Request.setAmount(amount);
        refundV3Request.setReason("取消订单");

        Result<WxPayRefundV3Result> result = refundV3(refundV3Request);
        if (!result.isSuccess()) {
            throw new WeshopWechatException(WeshopWechatResultStatus.WECHAT_PAY_FAIL);
        }
        return result.getData();
    }

    public String notify(String notifyData, SignatureHeader requestHeader) throws WxPayException {

        WxPayNotifyV3Result result = this.wxService.parseOrderNotifyV3Result(notifyData, requestHeader);
        // 解密后的数据
        WxPayNotifyV3Result.DecryptNotifyResult notifyResult = result.getResult();
        if (WxPayConstants.WxpayTradeStatus.SUCCESS.equals(notifyResult.getTradeState())) {
            String orderSN = notifyResult.getOutTradeNo();
            String transactionId = notifyResult.getTransactionId(); // 获取微信交易单号
            Order order = orderService.queryOneByCriteria(Criteria.of(Order.class).andEqualTo(Order::getOrderSn, orderSN));
            if (order == null) {
                return WechatConstants.XML_PAY_ORDER_NOT_FOUND;
            }

            // 更新订单状态、支付状态、交易单号和支付时间
            Date payTime = new Date(); // 当前时间作为支付时间
            int updateResult = orderService.updateNotNull(order
                .setOrderStatus(OrderStatusEnum.WAIT_SEND)
                .setPayStatus(PayStatusEnum.PAID)
                .setTransactionId(transactionId)  // 更新交易单号
                .setPayTime(payTime));            // 更新支付时间

            if (updateResult != 1) {
                return WechatConstants.XML_PAY_ORDER_NOT_FOUND;
            }

            // 支付成功后处理积分奖励
            handlePaymentSuccessRewards(order);

            log.info("【支付回调通知处理成功】订单号：{}，交易单号：{}，支付时间：{}", orderSN, transactionId, payTime);
        } else {
            return WxPayNotifyResponse.fail("支付失败");
        }
        if (WxPayConstants.WxpayTradeStatus.PAY_ERROR.equals(notifyResult.getTradeState())) {
            log.error("【支付回调通知失败】:{}", result);
            throw new WxPayException("微信支付-回调失败！");
        }
        return WxPayNotifyResponse.success("OK");
    }


    public boolean notify(Integer orderId) {

        Order order = orderService.queryById(orderId);

        if (order == null) {
            throw new WeshopPayException(WeshopWechatResultStatus.ORDER_NOT_EXIST);
        }

        // 更新订单状态、支付状态和支付时间
        Date payTime = new Date(); // 当前时间作为支付时间
        int updateResult = orderService.updateNotNull(order
            .setOrderStatus(OrderStatusEnum.WAIT_SEND)
            .setPayStatus(PayStatusEnum.PAID)
            .setPayTime(payTime));  // 更新支付时间

        if (updateResult != 1) {
            throw new WeshopPayException(WeshopWechatResultStatus.ORDER_NOT_EXIST);
        }

        // 支付成功后处理积分奖励
        handlePaymentSuccessRewards(order);

        log.info("【手动支付通知处理成功】订单ID：{}，支付时间：{}", orderId, payTime);

        return true;
    }


    @Resource
    private WxPayService wxService;

    public Result<WxPayOrderQueryResult> queryOrder(String transactionId, String outTradeNo) {
        try {
            return Result.success(wxService.queryOrder(transactionId, outTradeNo));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayOrderQueryResult> queryOrder(WxPayOrderQueryRequest wxPayOrderQueryRequest) {
        try {
            return Result.success(wxService.queryOrder(wxPayOrderQueryRequest));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayOrderCloseResult> closeOrder(String outTradeNo) {
        try {
            return Result.success(wxService.closeOrder(outTradeNo));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayOrderCloseResult> closeOrder(WxPayOrderCloseRequest wxPayOrderCloseRequest) {
        try {
            return Result.success(wxService.closeOrder(wxPayOrderCloseRequest));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayUnifiedOrderResult> unifiedOrder(@RequestBody WxPayUnifiedOrderRequest request) {
        try {
            return Result.success(wxService.unifiedOrder(request));
        } catch (WxPayException e) {
            log.error("调用微信统一下单接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }


    public <T> Result<T> createOrder(WxPayUnifiedOrderV3Request request) {
        try {
            return Result.success(wxService.createOrderV3(TradeTypeEnum.JSAPI, request));
        } catch (WxPayException e) {
            log.error("调用统一下单接口，并组装生成支付所需参数对象失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }


    public Result<WxPayRefundV3Result> refundV3(WxPayRefundV3Request request) {
        try {
            return Result.success(wxService.refundV3(request));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayOrderReverseV3Result> reverseOrderV3(String code) {
        try {
            return Result.success(wxService.reverseOrderV3(code));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayRefundQueryResult> refundQuery(String transactionId, String outTradeNo, String outRefundNo, String refundId) {
        try {
            return Result.success(wxService.refundQuery(transactionId, outTradeNo, outRefundNo, refundId));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayRefundQueryResult> refundQuery(WxPayRefundQueryRequest wxPayRefundQueryRequest) {
        try {
            return Result.success(wxService.refundQuery(wxPayRefundQueryRequest));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayOrderNotifyResult> parseOrderNotifyResult(String xmlData) {
        try {
            return Result.success(wxService.parseOrderNotifyResult(xmlData));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayRefundNotifyResult> parseRefundNotifyResult(String xmlData) {
        try {
            return Result.success(wxService.parseRefundNotifyResult(xmlData));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxScanPayNotifyResult> parseScanPayNotifyResult(String xmlData) {
        try {
            return Result.success(wxService.parseScanPayNotifyResult(xmlData));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    /* public Result<WxPaySendRedpackResult> sendRedpack(WxPaySendRedpackRequest request) {
         try {
             return Result.success(wxService.sendRedpack(request));
         } catch (WxPayException e) {
             log.error("调用微信支付接口失败：{}", e.getMessage(), e);
             throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
         }
     }

     public Result<WxPayRedpackQueryResult> queryRedpack(String mchBillNo) {
         try {
             return Result.success(wxService.queryRedpack(mchBillNo));
         } catch (WxPayException e) {
             log.error("调用微信支付接口失败：{}", e.getMessage(), e);
             throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
         }
     }
 */
    public Result<byte[]> createScanPayQrcodeMode1(String productId, File logoFile, Integer sideLength) {
        try {
            return Result.success(wxService.createScanPayQrcodeMode1(productId, logoFile, sideLength));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public Result<String> createScanPayQrcodeMode1(String productId) {
        return Result.success(wxService.createScanPayQrcodeMode1(productId));
    }

    public Result<byte[]> createScanPayQrcodeModel(String codeUrl, File logoFile, Integer sideLength) {
        try {
            return Result.success(wxService.createScanPayQrcodeMode2(codeUrl, logoFile, sideLength));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public Result report(WxPayReportRequest request) {
        try {
            wxService.report(request);
            return Result.success();
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayBillResult> downloadBill(String billDate, String billType, String tarType, String deviceInfo) {
        try {
            return Result.success(wxService.downloadBill(billDate, billType, tarType, deviceInfo));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayBillResult> downloadBill(WxPayDownloadBillRequest wxPayDownloadBillRequest) {
        try {
            return Result.success(wxService.downloadBill(wxPayDownloadBillRequest));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayMicropayResult> micropay(WxPayMicropayRequest request) {
        try {
            return Result.success(wxService.micropay(request));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayOrderReverseResult> reverseOrder(WxPayOrderReverseRequest request) {
        try {
            return Result.success(wxService.reverseOrder(request));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<String> getSandboxSignKey() {
        try {
            return Result.success(wxService.getSandboxSignKey());
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayCouponSendResult> sendCoupon(WxPayCouponSendRequest request) {
        try {
            return Result.success(wxService.sendCoupon(request));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayCouponStockQueryResult> queryCouponStock(WxPayCouponStockQueryRequest request) {
        try {
            return Result.success(wxService.queryCouponStock(request));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<WxPayCouponInfoQueryResult> queryCouponInfo(WxPayCouponInfoQueryRequest request) {
        try {
            return Result.success(wxService.queryCouponInfo(request));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }

    public Result<String> queryComment(Date beginDate, Date endDate, Integer offset, Integer limit) {
        try {
            return Result.success(wxService.queryComment(beginDate, endDate, offset, limit));
        } catch (WxPayException e) {
            log.error("调用微信支付接口失败：{}", e.getMessage(), e);
            throw new WeshopPayException(WechatPayResultStatus.WECHAT_PAY_FAIL);
        }
    }
    
    /**
     * 处理支付成功后的奖励逻辑
     * 包括：会员日积分奖励、普通消费积分奖励、推广佣金计算等
     */
    public void handlePaymentSuccessRewards(Order order) {
        try {
            log.info("开始处理订单{}的支付成功奖励", order.getId());
            
            // 1. 推广佣金计算（最重要，优先处理）
            paymentEventHandler.handlePaymentSuccess(order.getId());
            
            // 2. 普通消费积分奖励（基于实际支付金额）
            if (order.getActualPrice() != null && order.getActualPrice().compareTo(BigDecimal.ZERO) > 0) {
                boolean earnSuccess = pointsService.earnPointsFromOrder(
                    order.getUserId(), 
                    order.getId(), 
                    order.getActualPrice()
                );
                if (earnSuccess) {
                    log.info("订单{}普通消费积分奖励处理成功", order.getId());
                } else {
                    log.warn("订单{}普通消费积分奖励处理失败", order.getId());
                }
            }
            
            // 3. 会员日特殊积分奖励（每单件商品送500积分）
            handleMemberDayPointsReward(order);
            
        } catch (Exception e) {
            log.error("处理订单{}支付成功奖励失败，但不影响支付流程", order.getId(), e);
        }
    }
    
    /**
     * 处理会员日积分奖励（优化版本，增加幂等性检查）
     * 根据会员日小提示的规则：购买任意单件商品送500积分
     */
    private void handleMemberDayPointsReward(Order order) {
        try {
            // 检查是否为会员日（这里可以根据实际业务规则调整）
            if (isMemberDay()) {
                // 幂等性检查：避免重复奖励会员日积分
                QueryWrapper<PointsRecord> existWrapper = new QueryWrapper<>();
                existWrapper.eq("user_id", order.getUserId())
                           .eq("source_id", order.getId())
                           .eq("source", "manual")
                           .like("description", "会员日购物奖励");

                long existCount = pointsService.getPointsRecordMapper().selectCount(existWrapper);
                if (existCount > 0) {
                    log.warn("订单{}已经处理过会员日积分奖励，跳过重复处理", order.getId());
                    return;
                }

                // 获取订单商品数量
                int totalQuantity = getTotalGoodsQuantity(order.getId());

                if (totalQuantity > 0) {
                    // 每件商品送500积分
                    int bonusPoints = totalQuantity * 500;

                    // 创建会员日积分奖励记录
                    boolean success = pointsService.adjustUserPoints(
                        order.getUserId(),
                        bonusPoints,
                        String.format("会员日购物奖励：购买%d件商品获得%d积分", totalQuantity, bonusPoints)
                    );

                    if (success) {
                        log.info("订单{}会员日积分奖励成功：用户{}获得{}积分",
                            order.getId(), order.getUserId(), bonusPoints);
                    } else {
                        log.warn("订单{}会员日积分奖励失败", order.getId());
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理订单{}会员日积分奖励失败", order.getId(), e);
        }
    }
    
    /**
     * 判断是否为会员日
     * 这里简化为每周五为会员日，实际可以根据业务需求调整
     */
    private boolean isMemberDay() {
        // 简化实现：每周五为会员日
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        int dayOfWeek = calendar.get(java.util.Calendar.DAY_OF_WEEK);
        return dayOfWeek == java.util.Calendar.FRIDAY;
    }
    
    /**
     * 获取订单商品总数量
     */
    private int getTotalGoodsQuantity(Integer orderId) {
        try {
            // 这里需要查询订单商品表获取总数量
            // 简化实现，实际应该注入OrderGoodsService
            return 1; // 临时返回1，实际应该查询数据库
        } catch (Exception e) {
            log.error("获取订单{}商品数量失败", orderId, e);
            return 0;
        }
    }

}
