package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.entity.PromotionEarnings;
import com.logic.code.entity.User;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderGoods;
import com.logic.code.mapper.OrderMapper;
import com.logic.code.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 推广佣金计算服务
 * 负责在订单支付成功后计算和记录推广佣金
 */
@Service
@Slf4j
public class PromotionCommissionService {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private TieredPromotionService tieredPromotionService;
    
    @Autowired
    private PromotionEarningsService promotionEarningsService;
    
    @Autowired
    private OrderGoodsService orderGoodsService;
    
    /**
     * 处理订单支付成功后的推广佣金计算
     * @param orderId 订单ID
     * @return 是否处理成功
     */
    @Transactional
    public boolean processPromotionCommissionAfterPayment(Integer orderId) {
        try {
            log.info("开始处理订单{}的推广佣金计算", orderId);
            
            // 1. 获取订单信息
            Order order = orderMapper.selectById(orderId);
            if (order == null) {
                log.warn("订单不存在：{}", orderId);
                return false;
            }
            
            // 2. 检查订单是否有推广者
            if (order.getPromoterId() == null) {
                log.info("订单{}无推广者，跳过佣金计算", orderId);
                return true;
            }
            
            // 3. 检查是否已经计算过佣金
            if (order.getPromotionCommission() != null && order.getPromotionCommission().compareTo(BigDecimal.ZERO) > 0) {
                log.info("订单{}已计算过佣金，跳过重复计算", orderId);
                return true;
            }
            
            // 4. 获取推广者信息
            User promoter = userMapper.selectById(order.getPromoterId());
            if (promoter == null) {
                log.warn("推广者不存在：{}", order.getPromoterId());
                return false;
            }
            
            // 5. 获取订单商品信息
            List<OrderGoods> orderGoodsList = getOrderGoodsList(orderId);
            if (orderGoodsList.isEmpty()) {
                log.warn("订单{}无商品信息", orderId);
                return false;
            }
            
            // 6. 计算推广佣金
            return calculateAndRecordCommission(order, promoter, orderGoodsList);
            
        } catch (Exception e) {
            log.error("处理订单{}推广佣金失败", orderId, e);
            return false;
        }
    }
    
    /**
     * 计算并记录推广佣金
     */
    private boolean calculateAndRecordCommission(Order order, User promoter, List<OrderGoods> orderGoodsList) {
        try {
            // 获取主要商品信息（第一个商品）
            OrderGoods primaryGoods = orderGoodsList.get(0);
            Integer primaryGoodsId = primaryGoods.getGoodsId();
            String primaryGoodsName = primaryGoods.getGoodsName();
            
            // 使用阶梯推广服务计算佣金
            Map<String, Object> commissionResult = tieredPromotionService.calculateTieredCommission(
                promoter.getId(), primaryGoodsId, order.getActualPrice());
            
            // 检查是否需要购买资格
            Boolean requirePurchase = (Boolean) commissionResult.get("requirePurchase");
            if (requirePurchase != null && requirePurchase) {
                log.info("推广者{}未购买商品{}，无推广资格，跳过佣金计算", promoter.getId(), primaryGoodsId);
                return true; // 返回true表示处理完成，但不计算佣金
            }
            
            BigDecimal commissionAmount = (BigDecimal) commissionResult.get("commissionAmount");
            BigDecimal commissionRate = (BigDecimal) commissionResult.get("commissionRate");
            Boolean isTieredPromotion = (Boolean) commissionResult.get("isTieredPromotion");
            Integer promotionOrderCount = (Integer) commissionResult.get("promotionOrderCount");
            
            // 更新订单佣金信息
            order.setPromotionCommission(commissionAmount);
            orderMapper.updateById(order);
            
            // 创建推广收益记录
            if (isTieredPromotion) {
                createTieredPromotionEarnings(order, promoter.getId(), primaryGoodsId, 
                    primaryGoodsName, commissionResult);
                // 更新推广统计
                tieredPromotionService.updatePromotionStats(promoter.getId(), primaryGoodsId, commissionAmount, promotionOrderCount);
            } else {
                createNormalPromotionEarnings(order, promoter.getId(), primaryGoodsId, 
                    primaryGoodsName, commissionAmount, commissionRate);
            }
            
            log.info("订单{}推广佣金计算完成：推广者ID={}, 商品ID={}, 是否阶梯推广={}, 第{}笔, 返现比例={}%, 佣金金额={}", 
                order.getOrderSn(), promoter.getId(), primaryGoodsId, isTieredPromotion, 
                promotionOrderCount, commissionRate, commissionAmount);
            
            return true;
            
        } catch (Exception e) {
            log.error("计算订单{}推广佣金失败", order.getId(), e);
            return false;
        }
    }
    
    /**
     * 获取订单商品列表
     */
    private List<OrderGoods> getOrderGoodsList(Integer orderId) {
        QueryWrapper<OrderGoods> wrapper = new QueryWrapper<>();
        wrapper.eq("order_id", orderId);
        return orderGoodsService.queryList(wrapper);
    }
    
    /**
     * 创建阶梯推广收益记录
     */
    private void createTieredPromotionEarnings(Order order, Integer promoterId, Integer goodsId, 
                                             String goodsName, Map<String, Object> commissionResult) {
        try {
            PromotionEarnings earnings = new PromotionEarnings();
            earnings.setPromoterId(promoterId);
            earnings.setPromotedUserId(order.getUserId());
            earnings.setOrderId(order.getId());
            earnings.setOrderNo(order.getOrderSn());
            earnings.setOrderAmount(order.getActualPrice());
            earnings.setGoodsId(goodsId);
            earnings.setGoodsName(goodsName);
            earnings.setPromotionOrderCount((Integer) commissionResult.get("promotionOrderCount"));
            earnings.setIsTieredPromotion(true);
            earnings.setCommissionRate((BigDecimal) commissionResult.get("commissionRate"));
            earnings.setCommissionAmount((BigDecimal) commissionResult.get("commissionAmount"));
            earnings.setStatus("pending"); // 待确认状态，确认收货后变为confirmed
            earnings.setOrderCreateTime(order.getCreateTime());
            earnings.setCreateTime(new Date());
            earnings.setUpdateTime(new Date());
            earnings.setDescription("阶梯推广第" + commissionResult.get("promotionOrderCount") + "笔订单，返现比例" + commissionResult.get("commissionRate") + "%");
            earnings.setIsWithdrawn((byte) 0);
            
            promotionEarningsService.save(earnings);
            
            log.info("创建阶梯推广收益记录成功：推广者={}, 订单={}, 商品={}, 第{}笔", 
                promoterId, order.getOrderSn(), goodsId, commissionResult.get("promotionOrderCount"));
                
        } catch (Exception e) {
            log.error("创建阶梯推广收益记录失败：推广者={}, 订单={}", promoterId, order.getOrderSn(), e);
        }
    }
    
    /**
     * 创建普通推广收益记录
     */
    private void createNormalPromotionEarnings(Order order, Integer promoterId, Integer goodsId, 
                                             String goodsName, BigDecimal commissionAmount, BigDecimal commissionRate) {
        try {
            PromotionEarnings earnings = new PromotionEarnings();
            earnings.setPromoterId(promoterId);
            earnings.setPromotedUserId(order.getUserId());
            earnings.setOrderId(order.getId());
            earnings.setOrderNo(order.getOrderSn());
            earnings.setOrderAmount(order.getActualPrice());
            earnings.setGoodsId(goodsId);
            earnings.setGoodsName(goodsName);
            earnings.setPromotionOrderCount(1);
            earnings.setIsTieredPromotion(false);
            earnings.setCommissionRate(commissionRate);
            earnings.setCommissionAmount(commissionAmount);
            earnings.setStatus("pending"); // 待确认状态，确认收货后变为confirmed
            earnings.setOrderCreateTime(order.getCreateTime());
            earnings.setCreateTime(new Date());
            earnings.setUpdateTime(new Date());
            earnings.setDescription("普通推广订单，返现比例" + commissionRate + "%");
            earnings.setIsWithdrawn((byte) 0);
            
            promotionEarningsService.save(earnings);
            
            log.info("创建普通推广收益记录成功：推广者={}, 订单={}, 商品={}", 
                promoterId, order.getOrderSn(), goodsId);
                
        } catch (Exception e) {
            log.error("创建普通推广收益记录失败：推广者={}, 订单={}", promoterId, order.getOrderSn(), e);
        }
    }
    
    /**
     * 处理订单取消后的佣金回滚
     * @param orderId 订单ID
     * @return 是否处理成功
     */
    @Transactional
    public boolean rollbackPromotionCommissionAfterCancel(Integer orderId) {
        try {
            log.info("开始处理订单{}取消后的佣金回滚", orderId);
            
            // 1. 获取订单信息
            Order order = orderMapper.selectById(orderId);
            if (order == null || order.getPromoterId() == null) {
                log.info("订单{}无推广者或不存在，无需回滚", orderId);
                return true;
            }
            
            // 2. 查找相关的推广收益记录
            QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
            wrapper.eq("order_id", orderId);
            wrapper.eq("promoter_id", order.getPromoterId());
            List<PromotionEarnings> earningsList = promotionEarningsService.list(wrapper);
            
            // 3. 取消推广收益记录
            for (PromotionEarnings earnings : earningsList) {
                earnings.setStatus("cancelled");
                earnings.setUpdateTime(new Date());
                earnings.setDescription(earnings.getDescription() + " [订单已取消]");
                promotionEarningsService.updateById(earnings);
                
                // 如果是阶梯推广，需要回滚推广统计
                if (earnings.getIsTieredPromotion() && earnings.getGoodsId() != null) {
                    rollbackPromotionStats(earnings.getPromoterId(), earnings.getGoodsId(), 
                        earnings.getCommissionAmount(), earnings.getPromotionOrderCount());
                }
            }
            
            // 4. 清除订单佣金信息
            order.setPromotionCommission(BigDecimal.ZERO);
            orderMapper.updateById(order);
            
            log.info("订单{}佣金回滚完成，影响{}条推广收益记录", orderId, earningsList.size());
            return true;
            
        } catch (Exception e) {
            log.error("处理订单{}佣金回滚失败", orderId, e);
            return false;
        }
    }
    
    /**
     * 回滚推广统计数据
     */
    private void rollbackPromotionStats(Integer promoterId, Integer goodsId, 
                                      BigDecimal commissionAmount, Integer promotionOrderCount) {
        try {
            // 这里需要实现推广统计的回滚逻辑
            // 由于推广统计的复杂性，建议采用重新计算的方式而不是简单的减法
            log.info("回滚推广统计：推广者={}, 商品={}, 佣金={}, 推广次数={}", 
                promoterId, goodsId, commissionAmount, promotionOrderCount);
            
            // 可以调用TieredPromotionService的重新计算方法
            // tieredPromotionService.recalculatePromotionStats(promoterId, goodsId);
            
        } catch (Exception e) {
            log.error("回滚推广统计失败：推广者={}, 商品={}", promoterId, goodsId, e);
        }
    }
    
    /**
     * 订单确认收货后更新推广收益状态
     * @param orderId 订单ID
     */
    @Transactional
    public void confirmEarningsAfterOrderConfirm(Integer orderId) {
        try {
            log.info("开始处理订单{}确认收货后的推广收益状态更新", orderId);
            
            // 调用PromotionEarningsService的确认收益方法
            promotionEarningsService.confirmEarnings(orderId);
            
            log.info("订单{}确认收货后的推广收益状态更新完成", orderId);
            
        } catch (Exception e) {
            log.error("处理订单{}确认收货后的推广收益状态更新失败", orderId, e);
        }
    }
}