package com.logic.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.entity.ExpressCompany;
import com.logic.code.mapper.ExpressCompanyMapper;
import com.logic.code.model.vo.ExpressCompanyVO;
import com.logic.code.service.ExpressCompanyService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 快递公司服务实现
 * <AUTHOR>
 * @date 2025/1/27
 */
@Service
public class ExpressCompanyServiceImpl implements ExpressCompanyService {
    
    @Resource
    private ExpressCompanyMapper expressCompanyMapper;
    
    @Override
    public List<ExpressCompanyVO> getEnabledExpressCompanies() {
        List<ExpressCompany> companies = expressCompanyMapper.selectEnabledShippers();
        return convertToVOList(companies);
    }
    
    @Override
    public List<ExpressCompanyVO> getAllExpressCompanies() {
        List<ExpressCompany> companies = expressCompanyMapper.selectAllShippers();
        return convertToVOList(companies);
    }
    
    @Override
    public ExpressCompany getById(Integer id) {
        return expressCompanyMapper.selectById(id);
    }
    
    @Override
    public ExpressCompany getByCode(String code) {
        QueryWrapper<ExpressCompany> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("shipper_code", code);
        return expressCompanyMapper.selectOne(queryWrapper);
    }
    
    /**
     * 转换为VO列表
     * @param companies 快递公司实体列表
     * @return VO列表
     */
    private List<ExpressCompanyVO> convertToVOList(List<ExpressCompany> companies) {
        return companies.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
    
    /**
     * 转换为VO对象
     * @param company 快递公司实体
     * @return VO对象
     */
    private ExpressCompanyVO convertToVO(ExpressCompany company) {
        return ExpressCompanyVO.builder()
                .value(company.getShipperName())
                .code(company.getShipperCode())
                .id(company.getId())
                .sort(company.getSort())
                .build();
    }
}