package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.common.enmus.OrderStatusEnum;
import com.logic.code.common.enmus.PayStatusEnum;
import com.logic.code.entity.PromotionEarnings;
import com.logic.code.entity.User;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderGoods;
import com.logic.code.mapper.OrderMapper;
import com.logic.code.mapper.UserMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 推广收益补偿服务
 * 用于修复缺失的推广收益数据
 */
@Slf4j
@Service
public class PromotionCompensationService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PromotionEarningsService promotionEarningsService;

    @Autowired
    private OrderGoodsService orderGoodsService;

    @Autowired
    private TieredPromotionService tieredPromotionService;

    @Resource
    private JdbcTemplate jdbcTemplate;

    /**
     * 补偿缺失的推广收益数据
     * @return 补偿结果统计
     */
    @Transactional
    public Map<String, Object> compensateMissingEarnings() {
        log.info("开始执行推广收益数据补偿...");

        Map<String, Object> result = new HashMap<>();
        int totalOrders = 0;
        int compensatedOrders = 0;
        int skippedOrders = 0;
        int errorOrders = 0;

        try {
            // 1. 查询所有有推广者ID但没有推广收益记录的订单
            List<Order> orders = getOrdersWithoutEarnings();
            totalOrders = orders.size();

            log.info("找到 {} 个需要补偿的订单", totalOrders);

            // 2. 为每个订单创建推广收益记录
            for (Order order : orders) {
                try {
                    // 获取推广者ID（优先使用订单表中的，如果没有则从用户表获取）
                    Integer promoterId = getPromoterIdForOrder(order);
                    
                    // 检查订单是否有推广者
                    if (promoterId == null || promoterId <= 0) {
                        log.debug("订单 {} 无推广者，跳过", order.getId());
                        skippedOrders++;
                        continue;
                    }

                    // 获取推广者信息
                    User promoter = userMapper.selectById(promoterId);
                    if (promoter == null) {
                        log.warn("推广者 {} 不存在，跳过订单 {}", promoterId, order.getId());
                        skippedOrders++;
                        continue;
                    }

                    // 检查是否自己推广自己
                    if (promoter.getId().equals(order.getUserId())) {
                        log.warn("用户 {} 不能推广自己，跳过订单 {}", order.getUserId(), order.getId());
                        skippedOrders++;
                        continue;
                    }

                    // 获取订单商品信息
                    List<OrderGoods> orderGoodsList = getOrderGoodsList(order.getId());
                    if (orderGoodsList.isEmpty()) {
                        log.warn("订单 {} 无商品信息", order.getId());
                        skippedOrders++;
                        continue;
                    }

                    // 计算并创建推广收益记录
                    boolean success = calculateAndCreateEarnings(order, promoter, orderGoodsList);
                    if (success) {
                        compensatedOrders++;
                        log.debug("为订单 {} 创建推广收益记录成功", order.getId());
                    } else {
                        errorOrders++;
                        log.error("为订单 {} 创建推广收益记录失败", order.getId());
                    }

                } catch (Exception e) {
                    log.error("处理订单 {} 时发生异常: {}", order.getId(), e.getMessage(), e);
                    errorOrders++;
                }
            }

            result.put("success", true);
            result.put("message", "推广收益数据补偿完成");

        } catch (Exception e) {
            log.error("推广收益数据补偿失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "推广收益数据补偿失败: " + e.getMessage());
        }

        result.put("totalOrders", totalOrders);
        result.put("compensatedOrders", compensatedOrders);
        result.put("skippedOrders", skippedOrders);
        result.put("errorOrders", errorOrders);

        log.info("推广收益数据补偿完成 - 总订单数: {}, 补偿成功: {}, 跳过: {}, 错误: {}",
                totalOrders, compensatedOrders, skippedOrders, errorOrders);

        return result;
    }

    /**
     * 获取订单的推广者ID
     * 优先使用订单表中的promoter_id，如果没有则从用户表获取
     * @param order 订单对象
     * @return 推广者ID
     */
    private Integer getPromoterIdForOrder(Order order) {
        // 如果订单表中有推广者ID，直接使用
        if (order.getPromoterId() != null && order.getPromoterId() > 0) {
            return order.getPromoterId();
        }
        
        // 否则从用户表中获取推广者ID
        User user = userMapper.selectById(order.getUserId());
        if (user != null && user.getPromoterId() != null && user.getPromoterId() > 0) {
            // 更新订单表的promoter_id字段
            try {
                order.setPromoterId(user.getPromoterId());
                orderMapper.updateById(order);
                log.info("为订单 {} 补充推广者ID: {}", order.getId(), user.getPromoterId());
            } catch (Exception e) {
                log.warn("更新订单 {} 的推广者ID失败: {}", order.getId(), e.getMessage());
            }
            return user.getPromoterId();
        }
        
        return null;
    }

    /**
     * 获取没有推广收益记录的订单
     * 包括两种情况：
     * 1. 订单表中有promoter_id但没有推广收益记录的订单
     * 2. 订单表中没有promoter_id，但用户表中有推广者关系的订单
     */
    private List<Order> getOrdersWithoutEarnings() {
        // 使用自定义SQL查询处理复杂关联逻辑
        String sql = "SELECT o.* FROM weshop_order o " +
                "INNER JOIN weshop_user u ON o.user_id = u.id " +
                "WHERE ((o.promoter_id IS NOT NULL AND o.promoter_id > 0) " +
                "OR (u.promoter_id IS NOT NULL AND u.promoter_id > 0)) " +
                "AND NOT EXISTS (SELECT 1 FROM weshop_promotion_earnings pe WHERE pe.order_id = o.id) " +
                "ORDER BY o.create_time DESC";

        // 使用RowMapper来正确映射结果到Order对象
        return jdbcTemplate.query(sql, (rs, rowNum) -> {
            Order order = new Order();
            order.setId(rs.getInt("id"));
            order.setOrderSn(rs.getString("order_sn"));
            order.setUserId(rs.getInt("user_id"));
            order.setOrderStatus(OrderStatusEnum.valueOf(rs.getInt("order_status")));
            order.setShippingStatus(rs.getShort("shipping_status"));
            order.setPayStatus(PayStatusEnum.valueOf(rs.getInt("pay_status")));
            order.setConsignee(rs.getString("consignee"));
            order.setCountry(rs.getShort("country"));
            order.setProvince(rs.getShort("province"));
            order.setCity(rs.getShort("city"));
            order.setDistrict(rs.getShort("district"));
            order.setAddress(rs.getString("address"));
            order.setMobile(rs.getString("mobile"));
            order.setPostscript(rs.getString("postscript"));
            order.setShippingFee(rs.getBigDecimal("shipping_fee"));
            order.setPayName(rs.getString("pay_name"));
            order.setPayId(rs.getByte("pay_id"));
            order.setActualPrice(rs.getBigDecimal("actual_price"));
            order.setIntegral(rs.getInt("integral"));
            order.setIntegralMoney(rs.getBigDecimal("integral_money"));
            order.setOrderPrice(rs.getBigDecimal("order_price"));
            order.setGoodsPrice(rs.getBigDecimal("goods_price"));
            order.setCreateTime(rs.getTimestamp("create_time"));
            order.setConfirmTime(rs.getTimestamp("confirm_time"));
            order.setPayTime(rs.getTimestamp("pay_time"));
            order.setFreightPrice(rs.getBigDecimal("freight_price"));
            order.setCouponId(rs.getInt("coupon_id"));
            order.setParentId(rs.getInt("parent_id"));
            order.setCouponPrice(rs.getBigDecimal("coupon_price"));
            order.setCallbackStatus(rs.getString("callback_status"));
            order.setTransactionId(rs.getString("transaction_id"));
            order.setMchid(rs.getString("mchid"));
            order.setShippingInfoStatus(rs.getInt("shipping_info_status"));
            order.setShippingTime(rs.getTimestamp("shipping_time"));
            order.setConfirmReceiveTime(rs.getTimestamp("confirm_receive_time"));
            order.setPromoterId(rs.getInt("promoter_id"));
            order.setPromotionCommission(rs.getBigDecimal("promotion_commission"));
            order.setCommissionStatus(rs.getString("commission_status"));
            order.setBalancePrice(rs.getBigDecimal("balance_price"));
            return order;
        });
    }
    private List<OrderGoods> getOrderGoodsList(Integer orderId) {
        QueryWrapper<OrderGoods> wrapper = new QueryWrapper<>();
        wrapper.eq("order_id", orderId);
        return orderGoodsService.queryList(wrapper);
    }

    /**
     * 计算并创建推广收益记录
     */
    private boolean calculateAndCreateEarnings(Order order, User promoter, List<OrderGoods> orderGoodsList) {
        try {
            // 获取主要商品信息（第一个商品）
            OrderGoods primaryGoods = orderGoodsList.get(0);
            Integer primaryGoodsId = primaryGoods.getGoodsId();
            String primaryGoodsName = primaryGoods.getGoodsName();

            // 使用阶梯推广服务计算佣金
            Map<String, Object> commissionResult = tieredPromotionService.calculateTieredCommission(
                    promoter.getId(), primaryGoodsId, order.getActualPrice());

            // 检查是否需要购买资格
            Boolean requirePurchase = (Boolean) commissionResult.get("requirePurchase");
            if (requirePurchase != null && requirePurchase) {
                log.info("推广者{}未购买商品{}，无推广资格，跳过佣金计算", promoter.getId(), primaryGoodsId);
                return true; // 返回true表示处理完成，但不计算佣金
            }

            BigDecimal commissionAmount = (BigDecimal) commissionResult.get("commissionAmount");
            BigDecimal commissionRate = (BigDecimal) commissionResult.get("commissionRate");
            Boolean isTieredPromotion = (Boolean) commissionResult.get("isTieredPromotion");
            Integer promotionOrderCount = (Integer) commissionResult.get("promotionOrderCount");

            // 确保订单表中有推广者ID
            if (order.getPromoterId() == null || order.getPromoterId() <= 0) {
                order.setPromoterId(promoter.getId());
                orderMapper.updateById(order);
                log.info("为订单 {} 补充推广者ID: {}", order.getId(), promoter.getId());
            }

            // 创建推广收益记录
            if (isTieredPromotion) {
                createTieredPromotionEarnings(order, promoter.getId(), primaryGoodsId,
                        primaryGoodsName, commissionResult);
            } else {
                createNormalPromotionEarnings(order, promoter.getId(), primaryGoodsId,
                        primaryGoodsName, commissionAmount, commissionRate);
            }

            log.info("订单{}推广收益补偿完成：推广者ID={}, 商品ID={}, 是否阶梯推广={}, 第{}笔, 返现比例={}%, 佣金金额={}",
                    order.getOrderSn(), promoter.getId(), primaryGoodsId, isTieredPromotion,
                    promotionOrderCount, commissionRate, commissionAmount);

            return true;

        } catch (Exception e) {
            log.error("计算订单{}推广收益失败", order.getId(), e);
            return false;
        }
    }

    /**
     * 创建阶梯推广收益记录
     */
    private void createTieredPromotionEarnings(Order order, Integer promoterId, Integer goodsId,
                                             String goodsName, Map<String, Object> commissionResult) {
        try {
            PromotionEarnings earnings = new PromotionEarnings();
            earnings.setPromoterId(promoterId);
            earnings.setPromotedUserId(order.getUserId());
            earnings.setOrderId(order.getId());
            earnings.setOrderNo(order.getOrderSn());
            earnings.setOrderAmount(order.getActualPrice());
            earnings.setGoodsId(goodsId);
            earnings.setGoodsName(goodsName);
            earnings.setPromotionOrderCount((Integer) commissionResult.get("promotionOrderCount"));
            earnings.setIsTieredPromotion(true);
            earnings.setCommissionRate((BigDecimal) commissionResult.get("commissionRate"));
            earnings.setCommissionAmount((BigDecimal) commissionResult.get("commissionAmount"));
            earnings.setStatus(determineEarningsStatus(order)); // 根据订单状态确定收益状态
            earnings.setOrderCreateTime(order.getCreateTime());
            earnings.setCreateTime(new Date());
            earnings.setUpdateTime(new Date());
            earnings.setDescription("阶梯推广第" + commissionResult.get("promotionOrderCount") + "笔订单，返现比例" + commissionResult.get("commissionRate") + "%");
            earnings.setIsWithdrawn((byte) 0);

            // 如果订单已确认收货，设置确认时间和生效时间
            if (order.getConfirmReceiveTime() != null) {
                earnings.setConfirmTime(order.getConfirmReceiveTime());
                earnings.setEffectiveTime(order.getConfirmReceiveTime());
            }

            promotionEarningsService.save(earnings);

            log.info("创建阶梯推广收益记录成功：推广者={}, 订单={}, 商品={}, 第{}笔",
                    promoterId, order.getOrderSn(), goodsId, commissionResult.get("promotionOrderCount"));

        } catch (Exception e) {
            log.error("创建阶梯推广收益记录失败：推广者={}, 订单={}", promoterId, order.getOrderSn(), e);
            throw new RuntimeException("创建阶梯推广收益记录失败", e);
        }
    }

    /**
     * 创建普通推广收益记录
     */
    private void createNormalPromotionEarnings(Order order, Integer promoterId, Integer goodsId,
                                             String goodsName, BigDecimal commissionAmount, BigDecimal commissionRate) {
        try {
            PromotionEarnings earnings = new PromotionEarnings();
            earnings.setPromoterId(promoterId);
            earnings.setPromotedUserId(order.getUserId());
            earnings.setOrderId(order.getId());
            earnings.setOrderNo(order.getOrderSn());
            earnings.setOrderAmount(order.getActualPrice());
            earnings.setGoodsId(goodsId);
            earnings.setGoodsName(goodsName);
            earnings.setPromotionOrderCount(1);
            earnings.setIsTieredPromotion(false);
            earnings.setCommissionRate(commissionRate);
            earnings.setCommissionAmount(commissionAmount);
            earnings.setStatus(determineEarningsStatus(order)); // 根据订单状态确定收益状态
            earnings.setOrderCreateTime(order.getCreateTime());
            earnings.setCreateTime(new Date());
            earnings.setUpdateTime(new Date());
            earnings.setDescription("普通推广订单，返现比例" + commissionRate + "%");
            earnings.setIsWithdrawn((byte) 0);

            // 如果订单已确认收货，设置确认时间和生效时间
            if (order.getConfirmReceiveTime() != null) {
                earnings.setConfirmTime(order.getConfirmReceiveTime());
                earnings.setEffectiveTime(order.getConfirmReceiveTime());
            }

            promotionEarningsService.save(earnings);

            log.info("创建普通推广收益记录成功：推广者={}, 订单={}, 商品={}",
                    promoterId, order.getOrderSn(), goodsId);

        } catch (Exception e) {
            log.error("创建普通推广收益记录失败：推广者={}, 订单={}", promoterId, order.getOrderSn(), e);
            throw new RuntimeException("创建普通推广收益记录失败", e);
        }
    }

    /**
     * 根据订单状态确定收益状态
     */
    private String determineEarningsStatus(Order order) {
        // 如果订单已确认收货，收益状态为已确认
        if (order.getConfirmReceiveTime() != null) {
            return "confirmed";
        }

        // 如果订单已完成，收益状态为已确认
        if (order.getOrderStatus() != null && order.getOrderStatus().getValue() == 3) {
            return "confirmed";
        }

        // 如果订单已发货但未确认收货，收益状态为待确认
        if (order.getShippingTime() != null) {
            return "pending";
        }

        // 如果订单已支付但未发货，收益状态为待确认
        if (order.getPayTime() != null) {
            return "pending";
        }

        // 其他情况默认为待确认
        return "pending";
    }

    /**
     * 更新已确认收货订单的推广收益状态
     * @return 更新结果统计
     */
    @Transactional
    public Map<String, Object> updateConfirmedEarningsStatus() {
        log.info("开始更新已确认收货订单的推广收益状态...");

        Map<String, Object> result = new HashMap<>();
        int totalOrders = 0;
        int updatedRecords = 0;
        int errorRecords = 0;

        try {
            // 1. 查询所有已确认收货但推广收益状态仍为pending的订单
            String sql = "SELECT o.* FROM weshop_order o " +
                    "INNER JOIN weshop_promotion_earnings pe ON o.id = pe.order_id " +
                    "WHERE (o.confirm_receive_time IS NOT NULL OR o.order_status = " + OrderStatusEnum.COMPLETED.getValue() + ") " +
                    "AND pe.status = 'pending'";

            List<Order> orders = orderMapper.selectList(new QueryWrapper<Order>().apply(sql.replace("SELECT o.* FROM weshop_order o WHERE", "")));
            totalOrders = orders.size();

            log.info("找到 {} 个需要更新状态的订单", totalOrders);

            // 2. 更新每个订单的推广收益状态
            for (Order order : orders) {
                try {
                    // 更新推广收益状态
                    QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
                    wrapper.eq("order_id", order.getId());
                    wrapper.eq("status", "pending");

                    PromotionEarnings earnings = promotionEarningsService.getOne(wrapper);
                    if (earnings != null) {
                        earnings.setStatus("confirmed");
                        earnings.setConfirmTime(order.getConfirmReceiveTime() != null ? 
                                order.getConfirmReceiveTime() : new Date());
                        earnings.setEffectiveTime(order.getConfirmReceiveTime() != null ? 
                                order.getConfirmReceiveTime() : new Date());
                        earnings.setDescription(earnings.getDescription() + " [订单已确认收货]");
                        earnings.setUpdateTime(new Date());

                        boolean updated = promotionEarningsService.updateById(earnings);
                        if (updated) {
                            updatedRecords++;
                            log.debug("订单 {} 的推广收益状态更新成功", order.getId());
                        } else {
                            errorRecords++;
                            log.error("订单 {} 的推广收益状态更新失败", order.getId());
                        }
                    }

                } catch (Exception e) {
                    log.error("处理订单 {} 状态更新时发生异常: {}", order.getId(), e.getMessage(), e);
                    errorRecords++;
                }
            }

            result.put("success", true);
            result.put("message", "推广收益状态更新完成");

        } catch (Exception e) {
            log.error("推广收益状态更新失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "推广收益状态更新失败: " + e.getMessage());
        }

        result.put("totalOrders", totalOrders);
        result.put("updatedRecords", updatedRecords);
        result.put("errorRecords", errorRecords);

        log.info("推广收益状态更新完成 - 总订单数: {}, 更新成功: {}, 错误: {}",
                totalOrders, updatedRecords, errorRecords);

        return result;
    }
    
    /**
     * 获取补偿统计信息
     */
    public Map<String, Object> getMigrationStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 统计推广收益记录数
            long earningsCount = promotionEarningsService.count();
            
            // 统计有推广关系的用户数
            QueryWrapper<User> userWrapper = new QueryWrapper<>();
            userWrapper.isNotNull("promoter_id");
            long promotedUserCount = userMapper.selectCount(userWrapper);
            
            // 统计有推广者但没有推广收益记录的订单数
            long ordersWithoutEarningsCount = getOrdersWithoutEarnings().size();
            
            // 统计待确认状态的推广收益记录数
            QueryWrapper<PromotionEarnings> pendingWrapper = new QueryWrapper<>();
            pendingWrapper.eq("status", "pending");
            long pendingEarningsCount = promotionEarningsService.count(pendingWrapper);
            
            // 统计订单表中没有promoter_id但用户表中有推广关系的订单数
            long ordersWithUserPromoterCount = getOrdersWithUserPromoterOnly().size();
            
            stats.put("earningsRecordCount", earningsCount);
            stats.put("promotedUserCount", promotedUserCount);
            stats.put("ordersWithoutEarningsCount", ordersWithoutEarningsCount);
            stats.put("pendingEarningsCount", pendingEarningsCount);
            stats.put("ordersWithUserPromoterOnlyCount", ordersWithUserPromoterCount);
            
        } catch (Exception e) {
            log.error("获取补偿统计信息失败: {}", e.getMessage(), e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }
    
    /**
     * 获取订单表中没有promoter_id但用户表中有推广关系的订单
     */
    private List<Order> getOrdersWithUserPromoterOnly() {
        String sql = "SELECT o.* FROM weshop_order o " +
                "INNER JOIN weshop_user u ON o.user_id = u.id " +
                "WHERE (o.promoter_id IS NULL OR o.promoter_id = 0) " +
                "AND u.promoter_id IS NOT NULL AND u.promoter_id > 0 " +
                "AND NOT EXISTS (SELECT 1 FROM weshop_promotion_earnings pe WHERE pe.order_id = o.id)";
        
        return orderMapper.selectList(new QueryWrapper<Order>().apply(sql.replace("SELECT o.* FROM weshop_order o WHERE", "")));
    }
    
    /**
     * 批量补偿缺失的推广收益数据
     * @param batchSize 批处理大小
     * @return 补偿结果统计
     */
    @Transactional
    public Map<String, Object> compensateMissingEarningsBatch(int batchSize) {
        log.info("开始执行批量推广收益数据补偿，批处理大小: {}", batchSize);

        Map<String, Object> result = new HashMap<>();
        int totalProcessed = 0;
        int compensatedOrders = 0;
        int skippedOrders = 0;
        int errorOrders = 0;

        try {
            // 分批处理数据
            int offset = 0;
            List<Order> batchOrders;
            
            do {
                batchOrders = getOrdersWithoutEarningsBatch(batchSize, offset);
                int batchCount = batchOrders.size();
                
                if (batchCount == 0) {
                    break;
                }
                
                log.info("处理第 {}-{} 个订单", offset + 1, offset + batchCount);
                
                // 处理当前批次
                for (Order order : batchOrders) {
                    try {
                        // 获取推广者ID（优先使用订单表中的，如果没有则从用户表获取）
                        Integer promoterId = getPromoterIdForOrder(order);
                        
                        // 检查订单是否有推广者
                        if (promoterId == null || promoterId <= 0) {
                            log.debug("订单 {} 无推广者，跳过", order.getId());
                            skippedOrders++;
                            continue;
                        }

                        // 获取推广者信息
                        User promoter = userMapper.selectById(promoterId);
                        if (promoter == null) {
                            log.warn("推广者 {} 不存在，跳过订单 {}", promoterId, order.getId());
                            skippedOrders++;
                            continue;
                        }

                        // 检查是否自己推广自己
                        if (promoter.getId().equals(order.getUserId())) {
                            log.warn("用户 {} 不能推广自己，跳过订单 {}", order.getUserId(), order.getId());
                            skippedOrders++;
                            continue;
                        }

                        // 获取订单商品信息
                        List<OrderGoods> orderGoodsList = getOrderGoodsList(order.getId());
                        if (orderGoodsList.isEmpty()) {
                            log.warn("订单 {} 无商品信息", order.getId());
                            skippedOrders++;
                            continue;
                        }

                        // 计算并创建推广收益记录
                        boolean success = calculateAndCreateEarnings(order, promoter, orderGoodsList);
                        if (success) {
                            compensatedOrders++;
                            log.debug("为订单 {} 创建推广收益记录成功", order.getId());
                        } else {
                            errorOrders++;
                            log.error("为订单 {} 创建推广收益记录失败", order.getId());
                        }
                        
                        totalProcessed++;

                    } catch (Exception e) {
                        log.error("处理订单 {} 时发生异常: {}", order.getId(), e.getMessage(), e);
                        errorOrders++;
                    }
                }
                
                offset += batchSize;
                
                // 添加短暂延迟避免数据库压力过大
                Thread.sleep(100);
                
            } while (batchOrders.size() == batchSize);

            result.put("success", true);
            result.put("message", "批量推广收益数据补偿完成");

        } catch (Exception e) {
            log.error("批量推广收益数据补偿失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "批量推广收益数据补偿失败: " + e.getMessage());
        }

        result.put("totalProcessed", totalProcessed);
        result.put("compensatedOrders", compensatedOrders);
        result.put("skippedOrders", skippedOrders);
        result.put("errorOrders", errorOrders);

        log.info("批量推广收益数据补偿完成 - 总处理: {}, 补偿成功: {}, 跳过: {}, 错误: {}",
                totalProcessed, compensatedOrders, skippedOrders, errorOrders);

        return result;
    }
    
    /**
     * 获取缺失推广收益记录的订单（分批）
     * @param batchSize 批处理大小
     * @param offset 偏移量
     * @return 订单列表
     */
    private List<Order> getOrdersWithoutEarningsBatch(int batchSize, int offset) {
        // 使用MyBatis Plus的标准分页查询
        QueryWrapper<Order> wrapper = new QueryWrapper<>();
        wrapper.apply("EXISTS (SELECT 1 FROM weshop_user u WHERE weshop_order.user_id = u.id " +
                "AND ((weshop_order.promoter_id IS NOT NULL AND weshop_order.promoter_id > 0) " +
                "OR (u.promoter_id IS NOT NULL AND u.promoter_id > 0)))")
               .notExists("SELECT 1 FROM weshop_promotion_earnings pe WHERE pe.order_id = weshop_order.id")
               .orderByDesc("create_time");
        
        // 设置分页参数
        wrapper.last("LIMIT " + batchSize + " OFFSET " + offset);
        
        return orderMapper.selectList(wrapper);
    }
}