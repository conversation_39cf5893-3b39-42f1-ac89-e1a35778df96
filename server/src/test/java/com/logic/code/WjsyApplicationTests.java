package com.logic.code;

import cn.hutool.core.date.DateUtil;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.logic.code.common.Cache;
import com.logic.code.common.constants.WechatConstants;
import com.logic.code.common.enmus.OrderStatusEnum;
import com.logic.code.common.enmus.PayStatusEnum;
import com.logic.code.common.utils.ExcelUtils;
import com.logic.code.entity.User;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderGoods;
import com.logic.code.model.vo.OrderDetailVO;
import com.logic.code.schedule.TaskSchedule;
import com.logic.code.entity.goods.GoodsSpecification;
import com.logic.code.entity.goods.Specification;
import com.logic.code.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

@SpringBootTest
@Slf4j
class WjsyApplicationTests {

    @Resource
    WxMessageService wxMessageService;

    @Resource
    PayService payService;
    @Resource
    WxPayRemoteService wxPayRemoteService;
    @Resource
    private OrderService orderService;
    @Resource
    private UserService userService;

    @Resource
    private Cache cache;

    @Resource
    private TaskSchedule taskSchedule;

    @Resource
    private GoodsSpecificationService goodsSpecificationService;

    @Resource
    private SpecificationService specificationService;

    @Resource
    private PointsService pointsService;

    @Resource
    private PointsConsistencyScheduleService consistencyScheduleService;


    @Test
    public void pointTest(){
        pointsService.adjustUserPoints(79, 100, "测试");
    }
    @Test
    public void pointbq(){
        Map<String, Object> result = consistencyScheduleService.manualPointsConsistencyCheck(true);
        System.err.println(result);
        //pointsService.adjustUserPoints(79, 100, "测试");
    }

    @Test
    public void test() {
        cache.init();
        Order order = orderService.queryById(77);
        User user = userService.queryById(order.getUserId());
        OrderDetailVO orderDetailVO = orderService.queryOrderDetail(order.getId());
        wxMessageService.sendOrderSubscribeMessage(order, user, orderDetailVO.getOrderGoods(), true);
    }
    @Test
    public void noticeTest() {
        String orderSN = "WJYX2025081920080963534a88bc22f1";
        String transactionId = "4200002736202508195002331687"; // 获取微信交易单号
        Order order = orderService.queryOneByCriteria(Criteria.of(Order.class).andEqualTo(Order::getOrderSn, orderSN));
        if (order == null) {
            System.err.println(WechatConstants.XML_PAY_ORDER_NOT_FOUND);
            return;
        }

        // 更新订单状态、支付状态、交易单号和支付时间
        Date payTime = new Date(); // 当前时间作为支付时间
        int updateResult = orderService.updateNotNull(order
                .setOrderStatus(OrderStatusEnum.WAIT_SEND)
                .setPayStatus(PayStatusEnum.PAID)
                .setTransactionId(transactionId)  // 更新交易单号
                .setPayTime(payTime));            // 更新支付时间

        if (updateResult != 1) {
            System.err.println(WechatConstants.XML_PAY_ORDER_NOT_FOUND);
        }

        // 支付成功后处理积分奖励
        payService.handlePaymentSuccessRewards(order);

        log.info("【支付回调通知处理成功】订单号：{}，交易单号：{}，支付时间：{}", orderSN, transactionId, payTime);
    }

    @Test
    public void printOrderInfo() {
        cache.init();
        Order order = orderService.queryById(168);
        User user = userService.queryById(order.getUserId());
        OrderDetailVO orderDetailVO = orderService.queryOrderDetail(order.getId());
        wxMessageService.printOrderInfo(order, user, orderDetailVO.getOrderGoods(), true);
    }



    @Test
    void contextLoads() {

        //payService.refundV3(113);
        payService.reverseOrderV3("4499d18c0-202505311105325334096");
    }

    @Test
    void traceTest() {
        taskSchedule.test();
    }

    /**
     * 测试微信发货信息同步定时任务
     */
    @Test
    void testSyncShippingOrdersToWechat() {
        try {
            cache.init();
            System.out.println("开始测试微信发货信息同步定时任务...");
            taskSchedule.syncShippingOrdersToWechat();
            System.out.println("微信发货信息同步定时任务测试完成");
        } catch (Exception e) {
            System.err.println("微信发货信息同步定时任务测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    void pay() {
        payService.prepay(69);
    }

    @Test
    void createOrderV3() throws WxPayException {
        wxPayRemoteService.createOrderV3();
    }

    @Test
    void notice() {
        String xml = "{\"id\":\"b0bbce87-5861-5f48-b216-3cba746f0714\",\"create_time\":\"2025-05-30T20:35:27+08:00\",\"resource_type\":\"encrypt-resource\",\"event_type\":\"TRANSACTION.SUCCESS\",\"summary\":\"支付成功\",\"resource\":{\"original_type\":\"transaction\",\"algorithm\":\"AEAD_AES_256_GCM\",\"ciphertext\":\"3lY09PKfwJW+f2Ss1dXCl0ZxZ9ncei5qdW2y1YOkQjOnzdNuDvNcDts1S3iuXcVr/qJBTUrSV25m6RBloYWdWqi8W9KyGPuOMuN/Xea0P9Q8s1kLe/Dqj77rp+I/kOUGYhFZIUIM2PNaLdxyalrt0GyttsKTu0T63VvaiQNa3wMF1KFNGzF/VE1fI0xhjqSJLVptdyKzva7zjAscNJP76eIhjwT59DKb1xKVog1lloHn5rgDTqlCI01SU8ozIShBEe45o2dMwZqugW++HMR5v3e3clgRoMHo6csRc2PON/iu4mC4/Pt6sFLWQqVw8j8Sf72a5PKJdh2GLK8IpHCTApBgLgUrhJo6QbTRtMQz8Ykc9zG2eMgxA02RSd25sONjAVWV4J372zt5N1FELePrZuynj0EQZWKxtTQ838n1gQqiBt5ThEsQ5pd8UKAi8cMpyEnl/TpbakO6sfixIxMnswua8x5gJ9l89uFe8OhyiK6hyAuiREHUQmi0nf6SUKNspOmJ59Rg0OyQziI+rFx2XPrIdnyae6hPDN6+t94pCF4337vsgiYGQtqapwrzCqmErRcZWbFXdXxCYWQzSwE=\",\"associated_data\":\"transaction\",\"nonce\":\"pncgdwMQwkqd\"}}";
        //payService.notify(xml);
    }

    /**
     * 导出所有订单信息到Excel
     * 参考printOrderInfo方法，遍历所有订单并导出到Excel文件
     * 按时间段分组：上半场（前一日14点-24点）用绿色，下半场（当日0点-14点）用红色
     */
    @Test
    public void exportAllOrdersToExcel() {
        try {
            cache.init();

            // 获取所有订单
            List<Order> allOrders = orderService.queryAll();
            System.out.println("找到订单总数: " + allOrders.size());

            // 计算时间范围：昨天14点到今天14点
            Calendar yesterday14 = Calendar.getInstance();
            yesterday14.add(Calendar.DAY_OF_MONTH, -1);
            yesterday14.set(Calendar.HOUR_OF_DAY, 14);
            yesterday14.set(Calendar.MINUTE, 0);
            yesterday14.set(Calendar.SECOND, 0);
            yesterday14.set(Calendar.MILLISECOND, 0);

            Calendar today14 = Calendar.getInstance();
            today14.set(Calendar.HOUR_OF_DAY, 14);
            today14.set(Calendar.MINUTE, 0);
            today14.set(Calendar.SECOND, 0);
            today14.set(Calendar.MILLISECOND, 0);

            System.out.println("时间范围: " + yesterday14.getTime() + " 到 " + today14.getTime());

            // 过滤订单：只保留昨天14点到今天14点的订单，并按创建时间排序
            List<Order> filteredOrders = allOrders.stream()
                .filter(order -> {
                    if (order.getCreateTime() == null) {
                        return false;
                    }
                    if (order.getPayStatus().getValue() != 2) {
                        return false;
                    }
                    long orderTime = order.getCreateTime().getTime();
                    //return true;
                    //return orderTime >= yesterday14.getTimeInMillis();
                    return orderTime >= yesterday14.getTimeInMillis() && orderTime < today14.getTimeInMillis();
                    //return orderTime >=  today14.getTimeInMillis();
                })
                .sorted((o1, o2) -> {
                    // 按创建时间升序排序（最早的在前面）
                    if (o1.getCreateTime() == null && o2.getCreateTime() == null) {
                        return 0;
                    }
                    if (o1.getCreateTime() == null) {
                        return 1;
                    }
                    if (o2.getCreateTime() == null) {
                        return -1;
                    }
                    return o1.getCreateTime().compareTo(o2.getCreateTime());
                })
                .collect(java.util.stream.Collectors.toList());

            System.out.println("过滤后订单数量: " + filteredOrders.size());

            if (filteredOrders.isEmpty()) {
                System.out.println("在指定时间范围内没有找到任何订单数据");
                return;
            }

            // 创建Excel工作簿
            Workbook workbook = ExcelUtils.createWorkbook();
            Sheet sheet = workbook.createSheet("订单信息");

            // 创建样式
            CellStyle headerStyle = ExcelUtils.createHeaderStyle(workbook);
            CellStyle dataStyle = ExcelUtils.createDataStyle(workbook);
            CellStyle dateStyle = ExcelUtils.createDateStyle(workbook);

            // 创建上半场样式（绿色背景）
            CellStyle firstHalfStyle = workbook.createCellStyle();
            firstHalfStyle.setBorderBottom(BorderStyle.THIN);
            firstHalfStyle.setBorderLeft(BorderStyle.THIN);
            firstHalfStyle.setBorderRight(BorderStyle.THIN);
            firstHalfStyle.setBorderTop(BorderStyle.THIN);
            firstHalfStyle.setAlignment(HorizontalAlignment.LEFT);
            firstHalfStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            firstHalfStyle.setWrapText(true);
            firstHalfStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
            firstHalfStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 创建下半场样式（红色背景）
            CellStyle secondHalfStyle = workbook.createCellStyle();
            secondHalfStyle.setBorderBottom(BorderStyle.THIN);
            secondHalfStyle.setBorderLeft(BorderStyle.THIN);
            secondHalfStyle.setBorderRight(BorderStyle.THIN);
            secondHalfStyle.setBorderTop(BorderStyle.THIN);
            secondHalfStyle.setAlignment(HorizontalAlignment.LEFT);
            secondHalfStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            secondHalfStyle.setWrapText(true);
            secondHalfStyle.setFillForegroundColor(IndexedColors.CORAL.getIndex());
            secondHalfStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "日期时间段",  "订单编号",
                "收货人", "收货地址", "收货电话",
                "商品信息(含规格)",
                "创建时间", "备注"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                ExcelUtils.setCellValue(cell, headers[i], headerStyle);
            }

            // 填充数据
            int rowIndex = 1;
            for (Order order : filteredOrders) {
                try {
                    // 获取用户信息
                    User user = userService.queryById(order.getUserId());

                    // 获取订单详情
                    OrderDetailVO orderDetailVO = orderService.queryOrderDetail(order.getId());
                    List<OrderGoods> orderGoods = orderDetailVO.getOrderGoods();

                    // 判断订单时间段并选择样式
                    CellStyle rowStyle = dataStyle; // 默认样式
                    String timeSegment = "";

                    if (order.getCreateTime() != null) {
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        Calendar orderCal = Calendar.getInstance();
                        orderCal.setTime(order.getCreateTime());

                        // 获取今天0点的时间
                        Calendar todayMidnight = Calendar.getInstance();
                        todayMidnight.set(Calendar.HOUR_OF_DAY, 0);
                        todayMidnight.set(Calendar.MINUTE, 0);
                        todayMidnight.set(Calendar.SECOND, 0);
                        todayMidnight.set(Calendar.MILLISECOND, 0);

                        // 判断订单是在今天0点之前还是之后
                        if (order.getCreateTime().getTime() < todayMidnight.getTimeInMillis()) {
                            // 昨天14点到今天0点（绿色）- 显示昨天日期 + 时间段
                            rowStyle = firstHalfStyle;
                            Calendar yesterday = Calendar.getInstance();
                            yesterday.add(Calendar.DAY_OF_MONTH, -1);
                            timeSegment = dateFormat.format(yesterday.getTime()) + " 14:00-24:00";
                        } else {
                            // 今天0点到14点（红色）- 显示今天日期 + 时间段
                            rowStyle = secondHalfStyle;
                            timeSegment = dateFormat.format(new Date()) + " 00:00-14:00";
                        }
                    }

                    // 构建商品信息字符串（包含规格信息）
                    StringBuilder goodsInfo = new StringBuilder();
                    if (orderGoods != null && !orderGoods.isEmpty()) {
                        for (int i = 0; i < orderGoods.size(); i++) {
                            OrderGoods goods = orderGoods.get(i);
                            if (i > 0) goodsInfo.append("; ");

                            // 商品名称
                            goodsInfo.append(goods.getGoodsName());

                            // 根据goodsSpecificationIds获取规格信息
                            String specifications = getProductSpecifications(goods.getGoodsSpecificationIds());
                            if (!specifications.isEmpty()) {
                                goodsInfo.append(" [").append(specifications).append("]");
                            }

                            // 数量和价格
                            goodsInfo.append(" x").append(goods.getNumber())
                                    .append(" (¥").append(goods.getRetailPrice()).append(")");
                        }
                    }

                    // 构建完整地址
                    String fullAddress = "";
                    if (order.getProvince() != null && order.getCity() != null && order.getDistrict() != null) {
                        String provinceName = cache.region.get(order.getProvince());
                        String cityName = cache.region.get(order.getCity());
                        String districtName = cache.region.get(order.getDistrict());
                        fullAddress = ExcelUtils.safeString(provinceName) +
                                     ExcelUtils.safeString(cityName) +
                                     ExcelUtils.safeString(districtName) +
                                     ExcelUtils.safeString(order.getAddress());
                    }

                    Row dataRow = sheet.createRow(rowIndex++);
                    int cellIndex = 0;

                    // 填充每一列的数据，使用对应时间段的样式
                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), timeSegment, rowStyle);
                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getOrderSn(), rowStyle);
                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getConsignee(), rowStyle);
                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), fullAddress, rowStyle);
                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getMobile(), rowStyle);
                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), goodsInfo.toString(), rowStyle);

                    // 时间字段使用特殊的日期样式，但保持背景色
                    CellStyle timeStyle = workbook.createCellStyle();
                    timeStyle.cloneStyleFrom(dateStyle);
                    if (rowStyle == firstHalfStyle) {
                        timeStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
                        timeStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    } else if (rowStyle == secondHalfStyle) {
                        timeStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
                        timeStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    }

                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getCreateTime(), timeStyle);
                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getPostscript(), rowStyle);

                } catch (Exception e) {
                    System.err.println("处理订单 " + order.getId() + " 时出错: " + e.getMessage());
                    e.printStackTrace();
                }
            }

            // 自动调整列宽
            ExcelUtils.autoSizeColumns(sheet, headers.length);

            // 保存文件
            String date = DateUtil.format(new Date(), "yyyyMMddHHmmss");
            String fileName = "订单导出_" + date + ".xlsx";
            String filePath = System.getProperty("user.dir") + "/" + fileName;
            ExcelUtils.saveWorkbook(workbook, filePath);

            // 关闭工作簿
            workbook.close();

            System.out.println("订单导出完成！");
            System.out.println("导出文件路径: " + filePath);
            System.out.println("导出订单数量: " + (rowIndex - 1));

        } catch (IOException e) {
            System.err.println("导出Excel文件时出错: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("导出过程中出现异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 根据商品规格ID获取规格信息
     * @param goodsSpecificationIds 规格ID字符串，格式如 "1_2_3" 或 "1,2,3"
     * @return 格式化的规格信息字符串，如 "颜色:红色, 尺寸:L"
     */
    private String getProductSpecifications(String goodsSpecificationIds) {
        if (goodsSpecificationIds == null || goodsSpecificationIds.trim().isEmpty()) {
            return "";
        }

        List<String> specificationTexts = new ArrayList<>();

        try {
            // 支持两种分隔符：下划线和逗号
            String[] specIds;
            if (goodsSpecificationIds.contains("_")) {
                specIds = goodsSpecificationIds.split("_");
            } else {
                specIds = goodsSpecificationIds.split(",");
            }

            for (String specIdStr : specIds) {
                if (specIdStr.trim().isEmpty()) continue;

                try {
                    Integer specId = Integer.parseInt(specIdStr.trim());
                    GoodsSpecification goodsSpec = goodsSpecificationService.queryById(specId);
                    if (goodsSpec != null) {
                        // 获取规格名称
                        Specification specification = specificationService.findById(goodsSpec.getSpecificationId());
                        if (specification != null) {
                            specificationTexts.add(specification.getName() + ":" + goodsSpec.getValue());
                        }
                    }
                } catch (NumberFormatException e) {
                    System.err.println("无效的规格ID: " + specIdStr);
                }
            }
        } catch (Exception e) {
            System.err.println("解析商品规格ID时出错: " + goodsSpecificationIds + ", 错误: " + e.getMessage());
        }

        return String.join(", ", specificationTexts);
    }

}
